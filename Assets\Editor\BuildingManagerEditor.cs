using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(BuildingManager))]public class BuildingManagerEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        BuildingManager buildingManager = (BuildingManager)target;

        if (GUILayout.Button("Refresh Buildings"))
        {
            buildingManager.RefreshBuildings();
        }
        if (GUILayout.Button("Save Building Data"))
        {
            buildingManager.SaveBuildingData();
        }
        if (GUILayout.<PERSON><PERSON>("Open Building Manager Window"))
        {
            BuildingManagerWindow.ShowWindow();
        }
    }
}
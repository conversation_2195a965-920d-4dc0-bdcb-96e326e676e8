using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Test utilities for the Building Manager system
/// </summary>
public static class BuildingManagerTests
{
    [MenuItem("Tools/Building Manager/Run Tests")]
    public static void RunAllTests()
    {
        Debug.Log("=== Building Manager Tests ===");
        
        BuildingManager manager = FindBuildingManager();
        if (manager == null)
        {
            Debug.LogError("No BuildingManager found in scene. Please create one first.");
            return;
        }
        
        TestBuildingDataCreation(manager);
        TestBuildingManagement(manager);
        TestSaveLoad(manager);
        TestUIIntegration();
        
        Debug.Log("=== Tests Completed ===");
    }
    
    [MenuItem("Tools/Building Manager/Create Test Scene")]
    public static void CreateTestScene()
    {
        // Create BuildingManager
        GameObject managerGO = new GameObject("BuildingManager");
        BuildingManager manager = managerGO.AddComponent<BuildingManager>();
        
        // Create test buildings
        for (int i = 0; i < 3; i++)
        {
            GameObject buildingGO = new GameObject($"Test Building {i + 1}");
            buildingGO.transform.SetParent(managerGO.transform);
            buildingGO.transform.position = new Vector3(i * 5, 0, 0);
            
            Building building = buildingGO.AddComponent<Building>();
            
            // Create test bank levels
            building.bankLevels = new BankLevel[3];
            for (int level = 0; level < 3; level++)
            {
                building.bankLevels[level] = new BankLevel();
                
                // Create a simple cube for each level
                GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                cube.name = $"Level {level + 1}";
                cube.transform.SetParent(buildingGO.transform);
                cube.transform.localPosition = Vector3.up * level;
                cube.transform.localScale = Vector3.one * (1 + level * 0.2f);
                
                // Set different colors for each level
                Renderer renderer = cube.GetComponent<Renderer>();
                Material mat = new Material(Shader.Find("Standard"));
                mat.color = new Color(1f, 1f - level * 0.3f, level * 0.3f);
                renderer.material = mat;
                
                building.bankLevels[level].gameObjects = new GameObject[] { cube };
            }
            
            building.level = 0;
        }
        
        // Refresh the manager
        manager.RefreshBuildings();
        
        // Select the manager
        Selection.activeGameObject = managerGO;
        
        Debug.Log("Test scene created with 3 test buildings.");
    }
    
    private static BuildingManager FindBuildingManager()
    {
        return Object.FindFirstObjectByType<BuildingManager>();
    }
    
    private static void TestBuildingDataCreation(BuildingManager manager)
    {
        Debug.Log("Testing building data creation...");
        
        int initialCount = manager.GetAllBuildingData().Count;
        manager.RefreshBuildings();
        int afterRefreshCount = manager.GetAllBuildingData().Count;
        
        if (afterRefreshCount >= initialCount)
        {
            Debug.Log($"✓ Building data creation: {afterRefreshCount} buildings found");
        }
        else
        {
            Debug.LogError($"✗ Building data creation failed: Expected >= {initialCount}, got {afterRefreshCount}");
        }
        
        // Test individual building data
        List<BuildingData> buildings = manager.GetAllBuildingData();
        foreach (BuildingData building in buildings)
        {
            if (string.IsNullOrEmpty(building.id))
            {
                Debug.LogError($"✗ Building '{building.buildingName}' has no ID");
            }
            else
            {
                Debug.Log($"✓ Building '{building.buildingName}' has valid ID: {building.id}");
            }
        }
    }
    
    private static void TestBuildingManagement(BuildingManager manager)
    {
        Debug.Log("Testing building management operations...");
        
        List<BuildingData> buildings = manager.GetAllBuildingData();
        if (buildings.Count == 0)
        {
            Debug.LogWarning("No buildings to test management operations");
            return;
        }
        
        BuildingData testBuilding = buildings[0];
        string originalName = testBuilding.buildingName;
        int originalLevel = testBuilding.currentLevel;
        
        // Test renaming
        string newName = "Test Renamed Building";
        manager.RenameBuilding(testBuilding.id, newName);
        if (testBuilding.buildingName == newName)
        {
            Debug.Log("✓ Building renaming works");
        }
        else
        {
            Debug.LogError("✗ Building renaming failed");
        }
        
        // Test level changes
        if (testBuilding.maxLevel > 1)
        {
            manager.LevelUpBuilding(testBuilding.id);
            if (testBuilding.currentLevel == originalLevel + 1)
            {
                Debug.Log("✓ Building level up works");
                
                manager.LevelDownBuilding(testBuilding.id);
                if (testBuilding.currentLevel == originalLevel)
                {
                    Debug.Log("✓ Building level down works");
                }
                else
                {
                    Debug.LogError("✗ Building level down failed");
                }
            }
            else
            {
                Debug.LogError("✗ Building level up failed");
            }
        }
        
        // Restore original name
        manager.RenameBuilding(testBuilding.id, originalName);
    }
    
    private static void TestSaveLoad(BuildingManager manager)
    {
        Debug.Log("Testing save/load functionality...");
        
        // Get original data
        List<BuildingData> originalBuildings = manager.GetAllBuildingData();
        int originalCount = originalBuildings.Count;
        
        // Save data
        manager.SaveBuildingData();
        Debug.Log("✓ Data saved to PlayerPrefs");
        
        // Clear data
        manager.ClearBuildingData();
        if (manager.GetAllBuildingData().Count == 0)
        {
            Debug.Log("✓ Data cleared successfully");
        }
        else
        {
            Debug.LogError("✗ Data clear failed");
        }
        
        // Load data
        manager.LoadBuildingData();
        List<BuildingData> loadedBuildings = manager.GetAllBuildingData();
        
        if (loadedBuildings.Count == originalCount)
        {
            Debug.Log($"✓ Data loaded successfully: {loadedBuildings.Count} buildings");
        }
        else
        {
            Debug.LogError($"✗ Data load failed: Expected {originalCount}, got {loadedBuildings.Count}");
        }
    }
    
    private static void TestUIIntegration()
    {
        Debug.Log("Testing UI integration...");
        
        // Check if BuildingManagerWindow can be opened
        try
        {
            BuildingManagerWindow window = EditorWindow.GetWindow<BuildingManagerWindow>("Building Manager Test");
            if (window != null)
            {
                Debug.Log("✓ BuildingManagerWindow can be opened");
                window.Close();
            }
            else
            {
                Debug.LogError("✗ BuildingManagerWindow failed to open");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"✗ BuildingManagerWindow error: {e.Message}");
        }
        
        // Test highlighting system
        try
        {
            BuildingHighlighter.SetEnabled(true);
            BuildingHighlighter.SetEnabled(false);
            Debug.Log("✓ Building highlighting system works");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"✗ Building highlighting error: {e.Message}");
        }
    }
    
    [MenuItem("Tools/Building Manager/Clear Test Data")]
    public static void ClearTestData()
    {
        BuildingManager manager = FindBuildingManager();
        if (manager != null)
        {
            manager.ClearBuildingData();
            Debug.Log("Test data cleared.");
        }
        
        // Clear PlayerPrefs
        PlayerPrefs.DeleteKey("BuildingManagerData");
        Debug.Log("PlayerPrefs cleared.");
    }
}

using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Building))]public class BuildingEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        Building building = (Building)target;

        GUILayout.BeginHorizontal();

        GUI.backgroundColor = Color.red;
        if (GUILayout.Button("Previous Level"))
        {
            building.PreviousLevel();
        }

        GUI.backgroundColor = Color.green;
        if (GUILayout.Button("Next Level"))
        {
            building.NextLevel();
        }
        
        GUILayout.EndHorizontal();
        GUILayout.BeginHorizontal();
        
        GUI.backgroundColor = Color.cyan;
        if (GUILayout.But<PERSON>("First Level"))
        {
            building.FirstLevel();
        }
        
        GUI.backgroundColor = Color.yellow;
        if (GUILayout.Button("Last Level"))
        {
            building.LastLevel();
        }
        GUI.backgroundColor = Color.white;

        GUILayout.EndHorizontal();
    }
}

# Building Manager System

A comprehensive building management system for Unity that allows you to store, manage, and modify building data with a user-friendly editor interface.

## Features

- **Building Data Storage**: Store comprehensive building information including name, type, level, position, and custom properties
- **Level Management**: Easily manage building levels with visual feedback
- **UI Editor Window**: Intuitive editor interface for managing all buildings in your scene
- **Save/Load System**: Persistent data storage using PlayerPrefs or file system
- **Building Highlighting**: Visual highlighting system for easy building identification
- **Real-time Updates**: Automatic synchronization between scene objects and data

## Quick Start

### 1. Setup
1. Add a `BuildingManager` component to a GameObject in your scene
2. Ensure your building GameObjects have the `Building` component attached
3. Configure the `bankLevels` array in each Building component with the appropriate GameObjects for each level

### 2. Open the Building Manager Window
- Go to `Tools > Building Manager` in the Unity menu bar
- The window will automatically find the BuildingManager in your scene

### 3. Managing Buildings
- **View Buildings**: All buildings are listed in the left panel
- **Select Buildings**: Click on a building in the list to select it in the scene
- **Edit Properties**: Use the right panel to modify building name, type, level, and description
- **Level Control**: Use the level buttons to change building levels
- **Create Buildings**: Use the "Create from Prefab" or "Create Empty" buttons

## Components

### BuildingManager
The main component that manages all building data and operations.

**Key Methods:**
- `RefreshBuildings()`: Updates building data from scene objects
- `GetAllBuildingData()`: Returns all building data
- `SetBuildingLevel(id, level)`: Changes a building's level
- `RenameBuilding(id, name)`: Renames a building
- `SaveBuildingData()`: Saves data to PlayerPrefs
- `LoadBuildingData()`: Loads data from PlayerPrefs

### Building
Component attached to individual building GameObjects.

**Key Properties:**
- `level`: Current building level (0-based)
- `bankLevels`: Array of BankLevel objects containing GameObjects for each level

**Key Methods:**
- `NextLevel()`: Advances to next level
- `PreviousLevel()`: Goes to previous level
- `RefreshDisplay()`: Updates visual display

### BuildingData
Serializable data structure containing all building information.

**Properties:**
- `id`: Unique identifier
- `buildingName`: Display name
- `buildingType`: Building category
- `currentLevel`: Current level (0-based)
- `maxLevel`: Maximum available levels
- `position`, `rotation`, `scale`: Transform data
- `isActive`: Active state
- `description`: Text description
- `customProperties`: Dictionary for additional data

## Editor Tools

### Building Manager Window
Access via `Tools > Building Manager`

**Features:**
- Building list with search functionality
- Real-time building details editing
- Level management controls
- Building creation tools
- Save/load operations
- Building highlighting toggle

### Building Highlighter
Visual highlighting system for scene view.

**Features:**
- Wireframe highlighting of selected buildings
- Building name and level labels
- Customizable highlight colors
- Toggle on/off from the Building Manager window

### Testing Tools
Access via `Tools > Building Manager > Run Tests`

**Available Tests:**
- Building data creation validation
- Building management operations
- Save/load functionality
- UI integration testing

## Usage Examples

### Creating a New Building Programmatically
```csharp
BuildingManager manager = FindObjectOfType<BuildingManager>();

// Create GameObject
GameObject newBuilding = new GameObject("My Building");
Building building = newBuilding.AddComponent<Building>();

// Setup levels
building.bankLevels = new BankLevel[3];
// ... configure levels ...

// Refresh manager
manager.RefreshBuildings();
```

### Changing Building Level
```csharp
BuildingManager manager = FindObjectOfType<BuildingManager>();
string buildingId = "your-building-id";

// Level up
manager.LevelUpBuilding(buildingId);

// Set specific level
manager.SetBuildingLevel(buildingId, 2);
```

### Saving and Loading Data
```csharp
BuildingManager manager = FindObjectOfType<BuildingManager>();

// Save to PlayerPrefs
manager.SaveBuildingData();

// Load from PlayerPrefs
manager.LoadBuildingData();

// Save to file
manager.SaveBuildingDataToFile("path/to/file.json");

// Load from file
manager.LoadBuildingDataFromFile("path/to/file.json");
```

## Events

The BuildingManager provides events for real-time updates:

- `OnBuildingAdded`: Fired when a building is added
- `OnBuildingRemoved`: Fired when a building is removed
- `OnBuildingUpdated`: Fired when building data changes
- `OnBuildingsRefreshed`: Fired when the building list is refreshed

## Tips

1. **Performance**: Use `RefreshBuildings()` sparingly in runtime as it scans all child objects
2. **Persistence**: Building data is automatically saved to PlayerPrefs, but you can also use file-based saving
3. **Hierarchy**: Keep buildings as children of the BuildingManager GameObject for automatic detection
4. **Levels**: Ensure each BankLevel has the correct GameObjects assigned for proper level switching
5. **IDs**: Building IDs are automatically generated and managed by the system

## Troubleshooting

**Buildings not appearing in the list:**
- Ensure the Building component is attached
- Check that the GameObject is a child of the BuildingManager
- Try clicking "Refresh" in the Building Manager window

**Level switching not working:**
- Verify that bankLevels array is properly configured
- Check that GameObjects are assigned to each BankLevel
- Ensure the level index is within the valid range

**Data not persisting:**
- Check that SaveBuildingData() is being called
- Verify PlayerPrefs permissions in your build settings
- Try using file-based saving as an alternative

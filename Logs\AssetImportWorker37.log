Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker37
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker37.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [51624]  Target information:

Player connection [51624]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3203312488 [EditorId] 3203312488 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [51624] Host joined multi-casting on [***********:54997]...
Player connection [51624] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56820
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002863 seconds.
- Loaded All Assemblies, in  0.387 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 191 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.479 seconds
Domain Reload Profiling: 864ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (161ms)
		LoadAssemblies (112ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (156ms)
				TypeCache.ScanAssembly (141ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (479ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (440ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (265ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (38ms)
			ProcessInitializeOnLoadAttributes (90ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.768 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.885 seconds
Domain Reload Profiling: 1650ms
	BeginReloadAssembly (127ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (567ms)
		LoadAssemblies (342ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (234ms)
				TypeCache.ScanAssembly (213ms)
			BuildScriptInfoCaches (49ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (885ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (735ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (317ms)
			ProcessInitializeOnLoadMethodAttributes (304ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.0 MB). Loaded Objects now: 8010.
Memory consumption went from 188.8 MB to 181.8 MB.
Total: 11.904000 ms (FindLiveObjects: 1.196300 ms CreateObjectMapping: 0.948800 ms MarkObjects: 6.226600 ms  DeleteObjects: 3.528000 ms)

========================================================================
Received Import Request.
  Time since last request: 1567249.567951 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Plant_Vase_01B.fbx
  artifactKey: Guid(def8a145efc9c5b4dbe30b52238d11e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Plant_Vase_01B.fbx using Guid(def8a145efc9c5b4dbe30b52238d11e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a94e1701482013fed1aafddd6b705a98') in 0.5783249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Plinth_01B_Corner.fbx
  artifactKey: Guid(f4686d35c2409914c9520346338e22e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Plinth_01B_Corner.fbx using Guid(f4686d35c2409914c9520346338e22e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e4513057bba9525a7fbd80b76337d26b') in 0.029534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.012906 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Plinth_02A_Corner.fbx
  artifactKey: Guid(a53829bcd0353e54ea80624ba473b907) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Plinth_02A_Corner.fbx using Guid(a53829bcd0353e54ea80624ba473b907) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c4ed65dd35292b0667c53ce3efab929f') in 0.0225742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 2.006522 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Pot_01B.fbx
  artifactKey: Guid(689bc5fdf41945946aa7e40e65d5515f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Pot_01B.fbx using Guid(689bc5fdf41945946aa7e40e65d5515f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ecc012fa0ed247c1978fcb4a723b029') in 0.0278869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.067254 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rain_Spout_01B_End.fbx
  artifactKey: Guid(6e84c5eb8a54aef43a012f54b4816ee2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rain_Spout_01B_End.fbx using Guid(6e84c5eb8a54aef43a012f54b4816ee2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd8beb2d75f08f6ec86315f84aaefc99') in 0.0279136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.734000 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Road_Curve_01A.fbx
  artifactKey: Guid(40d11efb5c60eb54ab66cf0af52fb243) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Road_Curve_01A.fbx using Guid(40d11efb5c60eb54ab66cf0af52fb243) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3a60970e1cd446edb1854999f157f776') in 0.0249135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.066874 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Road_Straight_02A.fbx
  artifactKey: Guid(f24e4352dc63d1a4b993ebccaa6ce135) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Road_Straight_02A.fbx using Guid(f24e4352dc63d1a4b993ebccaa6ce135) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '863d541101ff5e87a406fb39db6df443') in 0.0267597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.707843 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_01A.fbx
  artifactKey: Guid(d5377d8c1024d7b4bb9f62f96ff642d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_01A.fbx using Guid(d5377d8c1024d7b4bb9f62f96ff642d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90f04a67039c0212649a7563cbb0b303') in 0.0254497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.107060 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_03A.fbx
  artifactKey: Guid(6371f7adb3b50db42a6b94f71051b726) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_03A.fbx using Guid(6371f7adb3b50db42a6b94f71051b726) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '485e7bb4a07a4382d21a0745c537a8c8') in 0.0244268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_04A.fbx
  artifactKey: Guid(62823fc5cd087304dbeaf656acf722f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_04A.fbx using Guid(62823fc5cd087304dbeaf656acf722f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcad16d87ac475713552ed27c92fb75b') in 0.0292629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.610938 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_04E.fbx
  artifactKey: Guid(153b8218a6bf5f7448c2abd43efa42b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_04E.fbx using Guid(153b8218a6bf5f7448c2abd43efa42b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ece6f3dd6d1101bce5054f84be266254') in 0.026029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.020302 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_06A.fbx
  artifactKey: Guid(47d006bc552b2514daf11b43422f1961) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_06A.fbx using Guid(47d006bc552b2514daf11b43422f1961) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '201a2d5c65b5cc4c6fa2bee0cf127240') in 0.0245499 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Sedan_01A.fbx
  artifactKey: Guid(038763c0aa56a2443a76fadf731c4162) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Sedan_01A.fbx using Guid(038763c0aa56a2443a76fadf731c4162) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '13c7a943dbd595d48625d488d64617e5') in 0.1584595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 86

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shelf_Box_02A.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shelf_Box_02A.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c268a654f27fe63ef18d537c01e21f0f') in 0.0266862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.464325 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shelves_01A.fbx
  artifactKey: Guid(ad3a22a2c1236284cbf2b649b52b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shelves_01A.fbx using Guid(ad3a22a2c1236284cbf2b649b52b0c8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46b3fe0396210efb10ae91b4d20e4a2a') in 0.0279289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.053354 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shelves_03C.fbx
  artifactKey: Guid(db1bc6aa1a7f184449469e8717af5339) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shelves_03C.fbx using Guid(db1bc6aa1a7f184449469e8717af5339) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66b2f5a2d4b9efa72780294ab92c3699') in 0.0264917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.379972 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shop_Logo_02A.fbx
  artifactKey: Guid(8b4b1eff4a553a24b84883efedee5419) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shop_Logo_02A.fbx using Guid(8b4b1eff4a553a24b84883efedee5419) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd0d2bb7f9ef62bdafcb530e31140beec') in 0.05444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 1.867819 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Sportscar_01B.fbx
  artifactKey: Guid(745926c073591c74b84c54f769414524) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Sportscar_01B.fbx using Guid(745926c073591c74b84c54f769414524) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '96cf1ad75e695427fe9973b23ddd9b02') in 0.1665571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 81

========================================================================
Received Import Request.
  Time since last request: 0.936133 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Standing_Mirror_01A.fbx
  artifactKey: Guid(d23625353d18c3f4a853af8f5045b431) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Standing_Mirror_01A.fbx using Guid(d23625353d18c3f4a853af8f5045b431) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1daef8d372e0214790e5a940993f8f94') in 0.033524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 2.631116 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Store_Box_02A.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Store_Box_02A.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ecacccc31819a05cc2fb697bcfe58e0') in 0.0276969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.986228 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Store_Fridge_01A.fbx
  artifactKey: Guid(90b9bff18ec098045b1413b22f4695b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Store_Fridge_01A.fbx using Guid(90b9bff18ec098045b1413b22f4695b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4853bbf773021922dce199de2ca4a658') in 0.0427267 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.301161 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetlight_02A.fbx
  artifactKey: Guid(58e67089e98a14e4a9c3865e47d5f7d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetlight_02A.fbx using Guid(58e67089e98a14e4a9c3865e47d5f7d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '554e30d256c5325e806db1d8920307d4') in 0.0379498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 2.112472 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetsign_5A.fbx
  artifactKey: Guid(e9c52cee9316f264087bfbd6bc262008) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetsign_5A.fbx using Guid(e9c52cee9316f264087bfbd6bc262008) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc1d85c284324240710917179cbf8ad9') in 0.0283806 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.073570 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetsign_11A.fbx
  artifactKey: Guid(5c11af0b735f83a4586256ecc1db622c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetsign_11A.fbx using Guid(5c11af0b735f83a4586256ecc1db622c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00f805fafaa2b23bc6f0f856d89a1bdb') in 0.0255181 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.088832 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_SUV_01C.fbx
  artifactKey: Guid(4fa9c7a0f5a434548b0d27ea48c9f808) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_SUV_01C.fbx using Guid(4fa9c7a0f5a434548b0d27ea48c9f808) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '35a6382417ad9caff180ed258b7cc41a') in 0.155944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 71

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Trash_Container_01A.fbx
  artifactKey: Guid(04408cd2f384a4d4f9d496a43e5fb543) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Trash_Container_01A.fbx using Guid(04408cd2f384a4d4f9d496a43e5fb543) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e2bd546535a7d3831d2c03db635a8439') in 0.0350395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_01B.fbx
  artifactKey: Guid(71d6b32c7cb92084c8e6e75f55d078db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_01B.fbx using Guid(71d6b32c7cb92084c8e6e75f55d078db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '02a05583eae8d9ea0d67f83a81006cff') in 0.0764903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 1.310082 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_02B.fbx
  artifactKey: Guid(61dc61b07104fef4589b8360d8a5219f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_02B.fbx using Guid(61dc61b07104fef4589b8360d8a5219f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5e979abeee4546930c2897f3b6b5c4a2') in 0.0381017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_03B.fbx
  artifactKey: Guid(c019953c9be67424fbdcb2331ba521d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_03B.fbx using Guid(c019953c9be67424fbdcb2331ba521d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b4f1f634dd4fc5be623dbe5793ec439d') in 0.0391204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_01A.fbx
  artifactKey: Guid(69f0f4e73a6f3164480ecf2c4d0ded10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_01A.fbx using Guid(69f0f4e73a6f3164480ecf2c4d0ded10) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9fd9b28237cf9b22f4f42ed3c325328') in 0.0492046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02C.fbx
  artifactKey: Guid(ae26ed45aa30fb44ab8717d2321c14ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02C.fbx using Guid(ae26ed45aa30fb44ab8717d2321c14ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4aa58394e234765174a6ca16c9830f22') in 0.0250192 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_01B.fbx
  artifactKey: Guid(74acfd840653d6c4a8dfe4853d159344) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_01B.fbx using Guid(74acfd840653d6c4a8dfe4853d159344) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '91cb6d18bd13418e35472d7e1383637b') in 0.0261336 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03B.fbx
  artifactKey: Guid(b72f8824d658c164aa385ca6789bb98b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03B.fbx using Guid(b72f8824d658c164aa385ca6789bb98b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6bcabbab1e3b618359f04b13b87edb86') in 0.0262624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03A.fbx
  artifactKey: Guid(52dfc0a39a9f7cf4a9d906ff68c5bd0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_03A.fbx using Guid(52dfc0a39a9f7cf4a9d906ff68c5bd0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dbe20b5fe168d7190902bdebafd4c294') in 0.0239346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Umbrella_01A.fbx
  artifactKey: Guid(3716cdb8fac9f7d4bbbd2c2a50c2ea8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Umbrella_01A.fbx using Guid(3716cdb8fac9f7d4bbbd2c2a50c2ea8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e4a98e4fbcb7a93a2b1ae62566bc8c1') in 0.0297134 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_02A.fbx
  artifactKey: Guid(452de0a740772384bacfb9a2ec400ea5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_02A.fbx using Guid(452de0a740772384bacfb9a2ec400ea5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4e6bdfaf54395fd61f1e0ab548618149') in 0.0359396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 1.063222 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wardrobe_02A.fbx
  artifactKey: Guid(6856da6c65fe54c44817d5bb332be333) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wardrobe_02A.fbx using Guid(6856da6c65fe54c44817d5bb332be333) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5a9c5c8578c9b4773500bd0d711e4f1c') in 0.0553818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.076609 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01C.fbx
  artifactKey: Guid(06d2351f42a797440acc80ea07932855) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01C.fbx using Guid(06d2351f42a797440acc80ea07932855) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '651f85904d71be595f63a04257f68291') in 0.033051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.683360 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01B.fbx
  artifactKey: Guid(11356e7b3cddc9b4e8d5d842d0bf0944) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01B.fbx using Guid(11356e7b3cddc9b4e8d5d842d0bf0944) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4e681732081dbdc971a2ce090949cdde') in 0.0288312 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 34 unused Assets / (3.1 MB). Loaded Objects now: 8193.
Memory consumption went from 165.8 MB to 162.7 MB.
Total: 12.385700 ms (FindLiveObjects: 0.485600 ms CreateObjectMapping: 0.427000 ms MarkObjects: 10.238700 ms  DeleteObjects: 1.232500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 262.650059 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_05A.fbx
  artifactKey: Guid(b9d63c67089afe242b3e69c7a7fbc3ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_05A.fbx using Guid(b9d63c67089afe242b3e69c7a7fbc3ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '907e3ca85b28707dcf4c2e26837dabba') in 0.0923776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Beam_01A.fbx
  artifactKey: Guid(cfc25871e784ed04e8692e0cb9c7db50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Beam_01A.fbx using Guid(cfc25871e784ed04e8692e0cb9c7db50) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4edb707030005e041eadfa0654a373e6') in 0.0361528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Washer_01A.fbx
  artifactKey: Guid(ff498d8b729b1034f9b43f4a4c642d2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Washer_01A.fbx using Guid(ff498d8b729b1034f9b43f4a4c642d2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e02fa740683116bf3d38f763fbf76cd3') in 0.0284445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wardrobe_01A.fbx
  artifactKey: Guid(d4a83d83a48a17b47a0f8a595faf2ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wardrobe_01A.fbx using Guid(d4a83d83a48a17b47a0f8a595faf2ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84195ff40393d0118157a784520b1004') in 0.0563284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01B.fbx
  artifactKey: Guid(e6ef77702056c4645a7e6698a74c1876) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01B.fbx using Guid(e6ef77702056c4645a7e6698a74c1876) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23c71643746b0c2e998fd103d7e9abd1') in 0.0375801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01C.fbx
  artifactKey: Guid(06d2351f42a797440acc80ea07932855) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01C.fbx using Guid(06d2351f42a797440acc80ea07932855) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'beabafd85c88739911a2554fbd49e993') in 0.0374231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01A.fbx
  artifactKey: Guid(8e5c96784f1d21d4cb1bc7d5a218c16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01A.fbx using Guid(8e5c96784f1d21d4cb1bc7d5a218c16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd535244116acddd4739a45575dbba9f8') in 0.02665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_04A.fbx
  artifactKey: Guid(34953f0c481b1d447ae88edd07d3071b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_04A.fbx using Guid(34953f0c481b1d447ae88edd07d3071b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'be501b3fbfb805e176e7943b89b1952c') in 0.0415016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wardrobe_02A.fbx
  artifactKey: Guid(6856da6c65fe54c44817d5bb332be333) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wardrobe_02A.fbx using Guid(6856da6c65fe54c44817d5bb332be333) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '31ea949201c99733bb8f8500f288e3e1') in 0.0609989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_02A.fbx
  artifactKey: Guid(452de0a740772384bacfb9a2ec400ea5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_02A.fbx using Guid(452de0a740772384bacfb9a2ec400ea5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e832b54bf808a02a0a8763dbe1c7c7ed') in 0.0409914 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
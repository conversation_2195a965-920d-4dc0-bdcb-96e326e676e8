Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker36
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker36.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [31928]  Target information:

Player connection [31928]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2781276349 [EditorId] 2781276349 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31928] Host joined multi-casting on [***********:54997]...
Player connection [31928] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56580
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.004295 seconds.
- Loaded All Assemblies, in  0.417 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 215 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.570 seconds
Domain Reload Profiling: 986ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (181ms)
		LoadAssemblies (132ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (176ms)
				TypeCache.ScanAssembly (163ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (570ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (529ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (284ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (123ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.707 seconds
Refreshing native plugins compatible for Editor in 2.03 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.770 seconds
Domain Reload Profiling: 1474ms
	BeginReloadAssembly (132ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (501ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (191ms)
				TypeCache.ScanAssembly (172ms)
			BuildScriptInfoCaches (50ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (771ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (616ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (174ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 5.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.8 MB). Loaded Objects now: 8010.
Memory consumption went from 187.0 MB to 179.1 MB.
Total: 14.780000 ms (FindLiveObjects: 1.500900 ms CreateObjectMapping: 1.119000 ms MarkObjects: 7.133600 ms  DeleteObjects: 5.024000 ms)

========================================================================
Received Import Request.
  Time since last request: 1567249.557485 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Plant_Vase_01A.fbx
  artifactKey: Guid(c5a193df276338945998bff203e569f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Plant_Vase_01A.fbx using Guid(c5a193df276338945998bff203e569f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '29e41f4c7c3875fc99334d9938d331f7') in 0.5640681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Plinth_01A_Corner.fbx
  artifactKey: Guid(054d5b41dbadcf3498c31860b7e2e6e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Plinth_01A_Corner.fbx using Guid(054d5b41dbadcf3498c31860b7e2e6e7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e8b5e94d84604db1a648b99036425e3c') in 0.0307595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.029648 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Plinth_02A.fbx
  artifactKey: Guid(416f70a2aa86a7d41afb6832c160061a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Plinth_02A.fbx using Guid(416f70a2aa86a7d41afb6832c160061a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e04eb66bf47f9ca4a764ccf4233b9f9') in 0.0244179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Plinth_02C.fbx
  artifactKey: Guid(f3b025fd22833c9458a0d3fa342ec6e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Plinth_02C.fbx using Guid(f3b025fd22833c9458a0d3fa342ec6e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a3eb3a07927e8b5c46dddca74666b70a') in 0.0275039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.952545 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Pot_01A.fbx
  artifactKey: Guid(abd91a601c83d6d47a3afc4654640089) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Pot_01A.fbx using Guid(abd91a601c83d6d47a3afc4654640089) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '350aee34a520b748205d766fb5c33a48') in 0.0265735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rain_Spout_01A.fbx
  artifactKey: Guid(7099b17ee967db247a0afc6d1476ecdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rain_Spout_01A.fbx using Guid(7099b17ee967db247a0afc6d1476ecdf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14b5a1d179a5edcf98d65da9e6105977') in 0.0259889 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.021657 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rain_Spout_01B.fbx
  artifactKey: Guid(8b1f1bb6e122fbc42b8121a50d11fb00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rain_Spout_01B.fbx using Guid(8b1f1bb6e122fbc42b8121a50d11fb00) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f61d6a19af45800a6db81d17a95d1a9') in 0.0247065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Register_01A.fbx
  artifactKey: Guid(025243b716bc0f64ebf6839304488adb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Register_01A.fbx using Guid(025243b716bc0f64ebf6839304488adb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb850d52f6b56a1f1d4c78c4056cdaea') in 0.0375761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.682309 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Road_Crossing_2A.fbx
  artifactKey: Guid(fe46f6d70716b484587e76c767771e32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Road_Crossing_2A.fbx using Guid(fe46f6d70716b484587e76c767771e32) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f911ba585da224573cfc4644fda4f00') in 0.0252883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Road_End_02A.fbx
  artifactKey: Guid(f6b2306fb09897843b968f5620588d44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Road_End_02A.fbx using Guid(f6b2306fb09897843b968f5620588d44) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f9dc31787b5ce1ba5c3f9ad330d6c8bb') in 0.0334648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.011973 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Road_Straight_01A.fbx
  artifactKey: Guid(64ad2c305fa55564c97e99c62e1b2aa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Road_Straight_01A.fbx using Guid(64ad2c305fa55564c97e99c62e1b2aa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6fdbfb0fe311981156136890a6c5ec06') in 0.0268554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rock_Small_01A.fbx
  artifactKey: Guid(561b89fedaf76ea4487b94a0bee25552) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rock_Small_01A.fbx using Guid(561b89fedaf76ea4487b94a0bee25552) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9fb29539495fed04e6fa003a2bfbae58') in 0.0341932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.649112 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rock_Small_03A.fbx
  artifactKey: Guid(68ee3daa97e36204e8afbfdbc9762908) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rock_Small_03A.fbx using Guid(68ee3daa97e36204e8afbfdbc9762908) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '808aad1ebd5fd76f8f6efde2e633351d') in 0.0252952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_02A.fbx
  artifactKey: Guid(9eb20a25dbcc49741be50b8581fbe93d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_02A.fbx using Guid(9eb20a25dbcc49741be50b8581fbe93d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9328db8a4fe40b7093d5ec4316c1ae5') in 0.0252724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.056653 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_02C.fbx
  artifactKey: Guid(5583e381ac1aea64aa5701676405d1b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_02C.fbx using Guid(5583e381ac1aea64aa5701676405d1b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '300056918564bfafedc79f198175d21c') in 0.06337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.626164 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_04D.fbx
  artifactKey: Guid(0f341069906438a48abf7567580aa120) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_04D.fbx using Guid(0f341069906438a48abf7567580aa120) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '936679aac6d5e242c353b03a84ab185b') in 0.0263722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_05C.fbx
  artifactKey: Guid(61fc2348abffdc140a0e6d6fbfc303cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_05C.fbx using Guid(61fc2348abffdc140a0e6d6fbfc303cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce8e7cd6a26dc6832a132ce8989abe35') in 0.0295929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Rug_06C.fbx
  artifactKey: Guid(588550cf5e565aa4fa6b2cab50d9300c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Rug_06C.fbx using Guid(588550cf5e565aa4fa6b2cab50d9300c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c0fd3c40f5b4bdda4d6efd4f5df01f1') in 0.0255512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.025410 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Sedan_01B.fbx
  artifactKey: Guid(a67a23953a3fa204c9801f4ead2f5f15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Sedan_01B.fbx using Guid(a67a23953a3fa204c9801f4ead2f5f15) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6235f2b3116b5cadf8c20dc924aaba7a') in 0.1621691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 86

========================================================================
Received Import Request.
  Time since last request: 0.470024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shelf_Box_02C.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shelf_Box_02C.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b5d7f707bd9f5d9f19e8dc2dd026c46') in 0.0240851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shelves_02B.fbx
  artifactKey: Guid(8abac7d79683ae6448455b693620cf52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shelves_02B.fbx using Guid(8abac7d79683ae6448455b693620cf52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a5957a5fdf3493695de2e48e46247b3d') in 0.0300169 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.004489 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shelves_03B.fbx
  artifactKey: Guid(a732266bd53156b41b3efdfec30e69d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shelves_03B.fbx using Guid(a732266bd53156b41b3efdfec30e69d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a0de8fbf6a30a98503cc15313fccb94') in 0.0248343 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shop_02A.fbx
  artifactKey: Guid(10c5494ff6a0414488d20c0285a1078d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shop_02A.fbx using Guid(10c5494ff6a0414488d20c0285a1078d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1520d336bb48f3613524028a32707151') in 0.0446291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 1.312972 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Shop_Logo_01A.fbx
  artifactKey: Guid(34e45318507a30c40977a026607bfd11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Shop_Logo_01A.fbx using Guid(34e45318507a30c40977a026607bfd11) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd981f3844eb2b0328c7937f3627902ed') in 0.0369878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Signpost_1A.fbx
  artifactKey: Guid(41e081ce88336d84087580d2b0df361f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Signpost_1A.fbx using Guid(41e081ce88336d84087580d2b0df361f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2ac9bde625071f6f872c2e1fba0bace8') in 0.0403911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.819206 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Sportscar_01A.fbx
  artifactKey: Guid(9912ff95dcf4a4c40a71e36d95cfcbc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Sportscar_01A.fbx using Guid(9912ff95dcf4a4c40a71e36d95cfcbc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ddc41c1cc72934a15f044479bbee4a73') in 0.1541784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 81

========================================================================
Received Import Request.
  Time since last request: 0.949371 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Standing_Lamp_02B.fbx
  artifactKey: Guid(6b341047c80d58b4fbdea5bbbbfa17b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Standing_Lamp_02B.fbx using Guid(6b341047c80d58b4fbdea5bbbbfa17b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf745ebdd8fd15703127eb01020fe9df') in 0.0317608 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Store_Box_01A.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Store_Box_01A.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9250be0881813b58f582a5c07a42045') in 0.0265905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 2.579591 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Store_Box_01C.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Store_Box_01C.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5011bd9c690dbafeeb41daccf3a7392f') in 0.0305365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Store_Box_02C.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Store_Box_02C.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2d53d935061d7289c6f2390461de2903') in 0.0318222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.928997 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Store_Box_03C.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Store_Box_03C.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '30b5f3bd447e3a2a0047d908cbea7040') in 0.0262335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Store_Shelves_02A.fbx
  artifactKey: Guid(15a6cab8542812d44a5797c829b0513a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Store_Shelves_02A.fbx using Guid(15a6cab8542812d44a5797c829b0513a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '57b1e447ac999e98daa536eab406b235') in 0.0522039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.238632 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetlight_01A.fbx
  artifactKey: Guid(5392965ff3cf3364fa6d787e35330a52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetlight_01A.fbx using Guid(5392965ff3cf3364fa6d787e35330a52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e0696801c7d3a6e03bd5a638ae8f0b23') in 0.0311699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetsign_2A.fbx
  artifactKey: Guid(d932818ee5ba6e448a7a8b339d0183e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetsign_2A.fbx using Guid(d932818ee5ba6e448a7a8b339d0183e0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ae66b0a808dd414e76f4bd1de394aac') in 0.0305621 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 2.065516 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetsign_4A.fbx
  artifactKey: Guid(c649e5b07aead5c459aace016b77e2ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetsign_4A.fbx using Guid(c649e5b07aead5c459aace016b77e2ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e1c74e32c22fbb30cdc627c516a9eaf0') in 0.0251147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetsign_8A.fbx
  artifactKey: Guid(d6ffe09877c19e74c8d9f8bb22c6e568) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetsign_8A.fbx using Guid(d6ffe09877c19e74c8d9f8bb22c6e568) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b32ef9f99451cb99d08fff55ae9e0938') in 0.0333044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.022742 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Streetsign_10A.fbx
  artifactKey: Guid(04c3e7515a9ada44987356e3cc1f4f7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Streetsign_10A.fbx using Guid(04c3e7515a9ada44987356e3cc1f4f7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7db34738b7bb4697f09d4a0c165a93f0') in 0.0267137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_SUV_01B.fbx
  artifactKey: Guid(8434022ec5e1d5f46be11a7b8f2faf99) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_SUV_01B.fbx using Guid(8434022ec5e1d5f46be11a7b8f2faf99) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a91ee02c808436e55d2d8b86d1e26b5a') in 0.1946738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 71

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Table_01A.fbx
  artifactKey: Guid(a8b3fbadf4314d74c8122463c86cffe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Table_01A.fbx using Guid(a8b3fbadf4314d74c8122463c86cffe6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6884f4e2918a633e7f88893e805868ec') in 0.0255413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Trash_Can_01A.fbx
  artifactKey: Guid(5e4d254ca86961240ad755eca2ca0e0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Trash_Can_01A.fbx using Guid(5e4d254ca86961240ad755eca2ca0e0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c70700e09900518def68d9fbdd07cbe') in 0.0349088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_01A.fbx
  artifactKey: Guid(4c4086ae0d1858d40b6480a3b304aab5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_01A.fbx using Guid(4c4086ae0d1858d40b6480a3b304aab5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ec8438f70440e3a99549406b7a0b09bd') in 0.0715905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 1.304924 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_02A.fbx
  artifactKey: Guid(7e85c59f892883441b0b6c845eebaa4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_02A.fbx using Guid(7e85c59f892883441b0b6c845eebaa4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '169be5bba46fc9aa63528cb870dfac3d') in 0.0372582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_03A.fbx
  artifactKey: Guid(d2ee1fc35cd4ced48923ef1ca73a5742) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_03A.fbx using Guid(d2ee1fc35cd4ced48923ef1ca73a5742) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '41d35b00259ce39ebb9a11bc82d78c95') in 0.0342317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_04B.fbx
  artifactKey: Guid(5a823ac9a04da0f418d6a3a4ebcf3216) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_04B.fbx using Guid(5a823ac9a04da0f418d6a3a4ebcf3216) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '884d556360992368088c95d790ce3bc8') in 0.0334666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Remote_01A.fbx
  artifactKey: Guid(05bb91124b2427546aa552e926afdc4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Remote_01A.fbx using Guid(05bb91124b2427546aa552e926afdc4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b229077ef5e1729ab1f792445c689ed') in 0.0244922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Clock_01B.fbx
  artifactKey: Guid(7d049d2f0f5e7664c804eb2bffbf2b5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Clock_01B.fbx using Guid(7d049d2f0f5e7664c804eb2bffbf2b5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c36eefc4476773c8bba7fdfc9d909605') in 0.0408758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_02B.fbx
  artifactKey: Guid(be813bceba66c774e9a5b2ed9f29ab68) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_02B.fbx using Guid(be813bceba66c774e9a5b2ed9f29ab68) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '160341ed462cc60e79fea432ddaa0043') in 0.0275548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_01A.fbx
  artifactKey: Guid(20e277ca82317864da5ef1ab856f38ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_01A.fbx using Guid(20e277ca82317864da5ef1ab856f38ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f34a7f4e62e4177eed0b5ba9aa972d47') in 0.0362127 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_04B.fbx
  artifactKey: Guid(67388dbfbc536204aa8904a461c18850) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_04B.fbx using Guid(67388dbfbc536204aa8904a461c18850) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8cfd0930bb341b4d132b5a1dc1044342') in 0.0254034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02B.fbx
  artifactKey: Guid(ab30082b3ff1fb641b186bcaacc48528) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Stand_02B.fbx using Guid(ab30082b3ff1fb641b186bcaacc48528) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c12ce825d2c27b4192e17208340a6c46') in 0.0299233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.055210 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wardrobe_01A.fbx
  artifactKey: Guid(d4a83d83a48a17b47a0f8a595faf2ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wardrobe_01A.fbx using Guid(d4a83d83a48a17b47a0f8a595faf2ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e20e13db222203622e05d7447813734f') in 0.0524594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.082378 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01B.fbx
  artifactKey: Guid(e6ef77702056c4645a7e6698a74c1876) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01B.fbx using Guid(e6ef77702056c4645a7e6698a74c1876) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8dc9219642056191439387da1e7e982') in 0.0314741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Beam_01B.fbx
  artifactKey: Guid(7ac30b8e106490f49bce376215476ea1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Beam_01B.fbx using Guid(7ac30b8e106490f49bce376215476ea1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a52caeaecc13fab25d65afc5801a3b1b') in 0.0266748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.638688 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01A.fbx
  artifactKey: Guid(8e5c96784f1d21d4cb1bc7d5a218c16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01A.fbx using Guid(8e5c96784f1d21d4cb1bc7d5a218c16f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f5d4b6bfecac867b63ed1151f5b3cae') in 0.0281477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 34 unused Assets / (3.1 MB). Loaded Objects now: 8193.
Memory consumption went from 165.6 MB to 162.5 MB.
Total: 12.321400 ms (FindLiveObjects: 0.464700 ms CreateObjectMapping: 0.458400 ms MarkObjects: 10.104200 ms  DeleteObjects: 1.292300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 262.658078 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_04B.fbx
  artifactKey: Guid(67388dbfbc536204aa8904a461c18850) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_04B.fbx using Guid(67388dbfbc536204aa8904a461c18850) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da069668a4cbf41691d3621e91508b0d') in 0.0923718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01B.fbx
  artifactKey: Guid(11356e7b3cddc9b4e8d5d842d0bf0944) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Table_01B.fbx using Guid(11356e7b3cddc9b4e8d5d842d0bf0944) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8002d339b99fd0c5e4e4d41c40eda063') in 0.0469953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Column_01B.fbx
  artifactKey: Guid(856b969c90b99ab40a60b4f2bee4777c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Column_01B.fbx using Guid(856b969c90b99ab40a60b4f2bee4777c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f7d327a6717210d30b2469ce21c71b8') in 0.0186258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wheelbarow_01A.fbx
  artifactKey: Guid(a7f9b23a3a65e9c4bb7c2a3b56d2a115) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wheelbarow_01A.fbx using Guid(a7f9b23a3a65e9c4bb7c2a3b56d2a115) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'feefa8b4592bfc9a57da408e128a4e2f') in 0.0385139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_01A.fbx
  artifactKey: Guid(20e277ca82317864da5ef1ab856f38ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_01A.fbx using Guid(20e277ca82317864da5ef1ab856f38ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6d281ed1e65bed13fd3a266b7b9c1db2') in 0.0378138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Column_01A.fbx
  artifactKey: Guid(784481b1049cbf64cabd0308cd4f3bc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Column_01A.fbx using Guid(784481b1049cbf64cabd0308cd4f3bc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '59c27e5a2b5624df6dd78f0568dddda1') in 0.0171466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_05B.fbx
  artifactKey: Guid(209c961ffbde84b4a98c3e5fed3ea3d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_05B.fbx using Guid(209c961ffbde84b4a98c3e5fed3ea3d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ca42f01ac4a5cd84df634f670786b668') in 0.0253972 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Welcome_Rug_01A.fbx
  artifactKey: Guid(95ef201ddaa686d42be95c70f58250ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Welcome_Rug_01A.fbx using Guid(95ef201ddaa686d42be95c70f58250ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21c3eb825b8cf11c197421183c82e931') in 0.0177864 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wood_Beam_01B.fbx
  artifactKey: Guid(7ac30b8e106490f49bce376215476ea1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wood_Beam_01B.fbx using Guid(7ac30b8e106490f49bce376215476ea1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '819f27e55e3f60dafe87c26a767511e5') in 0.0167283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Mailbox_01A.fbx
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Mailbox_01A.fbx using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd605c7601da1c7d05e70ec0d9d368c2b') in 0.0277766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_03A.fbx
  artifactKey: Guid(394d8c73976396d4fbc7b9661ae341ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Lamp_03A.fbx using Guid(394d8c73976396d4fbc7b9661ae341ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9d5d0c0c9a8ced453da6680d84f8cbe') in 0.0397349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01A.fbx
  artifactKey: Guid(a9cde69cdd88496458cee779ae20fb88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Window_Blinds_01A.fbx using Guid(a9cde69cdd88496458cee779ae20fb88) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9702bddeb58037a4fa30e87066283888') in 0.0371287 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// Editor utility for highlighting buildings in the scene view
/// </summary>
public static class BuildingHighlighter
{
    private static List<Building> highlightedBuildings = new List<Building>();
    private static Color highlightColor = Color.cyan;
    private static bool isEnabled = false;

    static BuildingHighlighter()
    {
        // Subscribe to scene GUI events
        SceneView.duringSceneGui += OnSceneGUI;
    }

    /// <summary>
    /// Enable or disable building highlighting
    /// </summary>
    public static void SetEnabled(bool enabled)
    {
        isEnabled = enabled;
        if (!enabled)
        {
            ClearHighlights();
        }
        SceneView.RepaintAll();
    }

    /// <summary>
    /// Highlight a specific building
    /// </summary>
    public static void HighlightBuilding(Building building)
    {
        if (building != null && !highlightedBuildings.Contains(building))
        {
            highlightedBuildings.Add(building);
            SceneView.RepaintAll();
        }
    }

    /// <summary>
    /// Remove highlight from a specific building
    /// </summary>
    public static void UnhighlightBuilding(Building building)
    {
        if (building != null && highlightedBuildings.Contains(building))
        {
            highlightedBuildings.Remove(building);
            SceneView.RepaintAll();
        }
    }

    /// <summary>
    /// Clear all highlights
    /// </summary>
    public static void ClearHighlights()
    {
        highlightedBuildings.Clear();
        SceneView.RepaintAll();
    }

    /// <summary>
    /// Set the highlight color
    /// </summary>
    public static void SetHighlightColor(Color color)
    {
        highlightColor = color;
        SceneView.RepaintAll();
    }

    /// <summary>
    /// Check if a building is highlighted
    /// </summary>
    public static bool IsBuildingHighlighted(Building building)
    {
        return highlightedBuildings.Contains(building);
    }

    private static void OnSceneGUI(SceneView sceneView)
    {
        if (!isEnabled || highlightedBuildings.Count == 0)
            return;

        // Draw highlights for each building
        foreach (Building building in highlightedBuildings)
        {
            if (building != null)
            {
                DrawBuildingHighlight(building);
            }
        }
    }

    private static void DrawBuildingHighlight(Building building)
    {
        // Get the building's bounds
        Bounds bounds = GetBuildingBounds(building);
        
        if (bounds.size.magnitude > 0)
        {
            // Draw wireframe cube around the building
            Handles.color = highlightColor;
            Handles.DrawWireCube(bounds.center, bounds.size);
            
            // Draw a label above the building
            Vector3 labelPosition = bounds.center + Vector3.up * (bounds.size.y * 0.5f + 1f);
            Handles.Label(labelPosition, building.name, EditorStyles.boldLabel);
            
            // Draw level indicator
            string levelText = $"Level {building.level + 1}";
            Vector3 levelPosition = bounds.center + Vector3.up * (bounds.size.y * 0.5f + 0.5f);
            Handles.Label(levelPosition, levelText, EditorStyles.miniLabel);
        }
    }

    private static Bounds GetBuildingBounds(Building building)
    {
        Bounds bounds = new Bounds(building.transform.position, Vector3.zero);
        
        // Get all renderers in the building and its children
        Renderer[] renderers = building.GetComponentsInChildren<Renderer>();
        
        if (renderers.Length > 0)
        {
            bounds = renderers[0].bounds;
            
            for (int i = 1; i < renderers.Length; i++)
            {
                if (renderers[i].gameObject.activeInHierarchy)
                {
                    bounds.Encapsulate(renderers[i].bounds);
                }
            }
        }
        else
        {
            // Fallback to a default size if no renderers found
            bounds.size = Vector3.one * 2f;
        }
        
        return bounds;
    }

    /// <summary>
    /// Draw building gizmos in the scene view
    /// </summary>
    [DrawGizmo(GizmoType.Selected | GizmoType.NonSelected)]
    static void DrawBuildingGizmos(Building building, GizmoType gizmoType)
    {
        if (!isEnabled) return;
        
        // Draw building info when selected
        if ((gizmoType & GizmoType.Selected) != 0)
        {
            Gizmos.color = Color.yellow;
            Bounds bounds = GetBuildingBounds(building);
            Gizmos.DrawWireCube(bounds.center, bounds.size);
        }
    }
}

/// <summary>
/// Custom property drawer for building highlighting controls
/// </summary>
[CustomPropertyDrawer(typeof(Building))]
public class BuildingPropertyDrawer : PropertyDrawer
{
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.PropertyField(position, property, label, true);
    }

    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        return EditorGUI.GetPropertyHeight(property, label, true);
    }
}

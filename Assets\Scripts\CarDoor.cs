using System;
using System.Collections;
using System.Net;
using StarterAssets;
using UnityEngine;

public class CarDoor : MonoBehaviour
{
    [Header("Door Settings")]
    public bool Driver = false;
    public bool isOpen = false;
    public bool playerInCar = false;
    public float openAngle = 90f;
    public float openSpeed = 2f;
    public Transform door;

    private Vector3 closedRotation;
    private Vector3 openRotation;
    private bool playerInRange = false;
    private GameObject player;
    public GameObject carBody;
    public Transform SittingPosition;
    public Transform OutsidePosition;
    public float transitionTime = 2.5f;

    void Start()
    {
        door = GetComponent<Transform>();
        // Store the initial rotation as the closed position
        closedRotation = door.eulerAngles;
        // Calculate the open rotation (rotate around Y-axis)
        openRotation = closedRotation + new Vector3(0, openAngle, 0);
    }

    void Update()
    {
        // Check for E key press when player is in range
        if (playerInRange && Input.GetKeyDown(KeyCode.E))
        {
            // Toggle door state
            isOpen = !isOpen;
            if (playerInCar == false)
            {
                StartCoroutine(GetInCar());
            }
            else
            {
                StartCoroutine(GetOutCar());
            }
        }

        // Smoothly rotate the door between open and closed positions
        Vector3 targetRotation = isOpen ? openRotation : closedRotation;
        door.eulerAngles = Vector3.Lerp(door.eulerAngles, targetRotation, openSpeed * Time.deltaTime);
        
    }

    private IEnumerator GetInCar()
    {
        if (Driver == true)
        {
            if (!transform.parent.GetComponent<DriveCar>().AddDriver(player))
            {
                Debug.Log("Car is full");
                yield break;
            }
        }else
        {
            if (!transform.parent.GetComponent<DriveCar>().AddPassenger(player))
            {
                Debug.Log("Car is full");
                yield break;
            }
        }
        playerInCar = true;
        player.GetComponent<Animator>().SetBool("Drive", true);
        carBody.GetComponent<MeshCollider>().enabled = false;
        player.GetComponent<CharacterController>().enabled = false;
        player.GetComponent<StarterAssetsInputs>().enabled = false;
        player.GetComponent<ThirdPersonController>().enabled = false;

        // Smoothly Transition player to sitting position
        Vector3 targetPosition = SittingPosition.position;
        Quaternion targetRotation = SittingPosition.rotation;
        float startTime = Time.time;
        while (Time.time - startTime < transitionTime)
        {
            player.transform.position = Vector3.Lerp(player.transform.position, targetPosition, (Time.time - startTime) / transitionTime);
            player.transform.rotation = Quaternion.Slerp(player.transform.rotation, targetRotation, (Time.time - startTime) / transitionTime);
            yield return null;
        }

        // Ensure final position is exact
        player.transform.position = targetPosition;
        player.transform.rotation = targetRotation;

        isOpen = false;
        
        yield break;
    }
    private IEnumerator GetOutCar()
    {
        playerInCar = false;
        player.GetComponent<Animator>().SetBool("Drive", false);
        carBody.GetComponent<MeshCollider>().enabled = true;

        // Remove player from car (this will unparent them)
        transform.parent.GetComponent<DriveCar>().RemovePlayer(player);

        // Smoothly transition player to outside position
        Vector3 targetPosition = OutsidePosition.position;
        Quaternion targetRotation = OutsidePosition.rotation;
        float startTime = Time.time;
        while (Time.time - startTime < transitionTime)
        {
            player.transform.position = Vector3.Lerp(player.transform.position, targetPosition, (Time.time - startTime) / transitionTime);
            player.transform.rotation = Quaternion.Slerp(player.transform.rotation, targetRotation, (Time.time - startTime) / transitionTime);
            yield return null;
        }

        // Ensure final position is exact
        player.transform.position = targetPosition;
        player.transform.rotation = targetRotation;

        // Re-enable player controls after transition is complete
        player.GetComponent<CharacterController>().enabled = true;
        player.GetComponent<StarterAssetsInputs>().enabled = true;
        player.GetComponent<ThirdPersonController>().enabled = true;
        yield break;
    }

    // Called when another collider enters the trigger
    void OnTriggerEnter(Collider other)
    {
        // Check if the colliding object has the "Player" tag
        if (other.CompareTag("Player"))
        {
            player = other.gameObject;
            playerInRange = true;
        }
    }

    // Called when another collider exits the trigger
    void OnTriggerExit(Collider other)
    {
        // Check if the colliding object has the "Player" tag
        if (other.CompareTag("Player"))
        {
            player = null;
            playerInRange = false;
        }
    }
}

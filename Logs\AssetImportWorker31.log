Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker31
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker31.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [23324]  Target information:

Player connection [23324]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3452717071 [EditorId] 3452717071 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [23324] Host joined multi-casting on [***********:54997]...
Player connection [23324] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56052
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003193 seconds.
- Loaded All Assemblies, in  0.396 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 193 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.506 seconds
Domain Reload Profiling: 901ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (167ms)
		LoadAssemblies (129ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (161ms)
				TypeCache.ScanAssembly (147ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (506ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (459ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (265ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (44ms)
			ProcessInitializeOnLoadAttributes (98ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.745 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.782 seconds
Domain Reload Profiling: 1524ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (21ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (537ms)
		LoadAssemblies (368ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (248ms)
			TypeCache.Refresh (185ms)
				TypeCache.ScanAssembly (166ms)
			BuildScriptInfoCaches (49ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (782ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (649ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (337ms)
			ProcessInitializeOnLoadMethodAttributes (198ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 3.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 205 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.2 MB). Loaded Objects now: 7996.
Memory consumption went from 184.5 MB to 177.3 MB.
Total: 16.034600 ms (FindLiveObjects: 1.726400 ms CreateObjectMapping: 1.183000 ms MarkObjects: 8.051600 ms  DeleteObjects: 5.068100 ms)

========================================================================
Received Import Request.
  Time since last request: 1562856.517076 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01D.prefab
  artifactKey: Guid(d7bf6ed599f585c469a1e898f8bf257a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01D.prefab using Guid(d7bf6ed599f585c469a1e898f8bf257a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b429a04328ab7056570e5cdc13fab04f') in 0.5229951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 179

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03E.prefab
  artifactKey: Guid(5c8b757d02f3a6940a27a2be420d1c73) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03E.prefab using Guid(5c8b757d02f3a6940a27a2be420d1c73) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00cc1b6d2d8c98322a2e35ab035b318a') in 0.0331548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01C.prefab
  artifactKey: Guid(212177bae3b324b49aa4907cc84984c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01C.prefab using Guid(212177bae3b324b49aa4907cc84984c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f1f84fdc22d1d6170bc1754f20257fa5') in 0.0313826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03C.prefab
  artifactKey: Guid(1392404168a8cc44aae2f48e1e34b16d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03C.prefab using Guid(1392404168a8cc44aae2f48e1e34b16d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23831256a9d6f546033d501c41bfddb5') in 0.0284509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
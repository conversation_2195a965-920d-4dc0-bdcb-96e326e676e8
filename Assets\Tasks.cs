using UnityEngine;

public class Tasks : MonoBehaviour
{
    public static int taskCount = 0;
    public BuildingManager buildingManager;
    public int buildingIndex;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        buildingManager = FindFirstObjectByType<BuildingManager>();
    }

    // Update is called once per frame
    void Update()
    {

    }
    public void CompleteTask()
    {
        buildingManager.buildings[buildingIndex].NextLevel();
        taskCount++;
        Debug.Log("Task Number " + taskCount + " completed");
        buildingManager.buildings[0].SetLevel((int)(taskCount/5));
        Destroy(gameObject);
    }
}

﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Edgegap", "Edgegap.csproj", "{E8D2805A-5936-732C-31DF-F873ABF83608}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Mirror.CodeGen", "Unity.Mirror.CodeGen.csproj", "{5948868A-4ED6-91D1-4C9B-34E6369DD86B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Components", "Mirror.Components.csproj", "{342107B3-CB5B-F986-4F75-20C369CB20C2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Editor", "Mirror.Editor.csproj", "{9B2F8E56-0892-AACE-9992-7128731FB8B9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{42420588-391A-AC56-C603-C49C71C490D6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Examples", "Mirror.Examples.csproj", "{8E575E0A-2CD3-9105-943C-9A3243CA9F5E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "kcp2k", "kcp2k.csproj", "{110441C6-038A-1FE8-BDE5-0FF9395CE5A5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror", "Mirror.csproj", "{7D176265-BF3D-8AF2-B287-6FCC8348FCE4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SimpleWebTransport", "SimpleWebTransport.csproj", "{A5296185-0CBE-F914-1F65-8CA162AA72F6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-Editor", "Assembly-CSharp-Editor.csproj", "{C07C6D7C-2660-AFB1-430B-9C256ECCF404}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Authenticators", "Mirror.Authenticators.csproj", "{877D9F31-1CAB-1BEC-3911-A28404FD47EC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Transports", "Mirror.Transports.csproj", "{420B8032-D778-4B73-028E-483CE5060308}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EncryptionTransportEditor", "EncryptionTransportEditor.csproj", "{186C9F35-36F5-D177-C774-40824D3C73FA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Telepathy", "Telepathy.csproj", "{02E51364-A3E5-F85A-3B7E-47829A87B53A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.CompilerSymbols", "Mirror.CompilerSymbols.csproj", "{82E29B65-EAF5-8036-392E-9A6644E599FA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E8D2805A-5936-732C-31DF-F873ABF83608}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E8D2805A-5936-732C-31DF-F873ABF83608}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E8D2805A-5936-732C-31DF-F873ABF83608}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E8D2805A-5936-732C-31DF-F873ABF83608}.Release|Any CPU.Build.0 = Release|Any CPU
		{5948868A-4ED6-91D1-4C9B-34E6369DD86B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5948868A-4ED6-91D1-4C9B-34E6369DD86B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5948868A-4ED6-91D1-4C9B-34E6369DD86B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5948868A-4ED6-91D1-4C9B-34E6369DD86B}.Release|Any CPU.Build.0 = Release|Any CPU
		{342107B3-CB5B-F986-4F75-20C369CB20C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{342107B3-CB5B-F986-4F75-20C369CB20C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{342107B3-CB5B-F986-4F75-20C369CB20C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{342107B3-CB5B-F986-4F75-20C369CB20C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B2F8E56-0892-AACE-9992-7128731FB8B9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B2F8E56-0892-AACE-9992-7128731FB8B9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B2F8E56-0892-AACE-9992-7128731FB8B9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B2F8E56-0892-AACE-9992-7128731FB8B9}.Release|Any CPU.Build.0 = Release|Any CPU
		{42420588-391A-AC56-C603-C49C71C490D6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{42420588-391A-AC56-C603-C49C71C490D6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{42420588-391A-AC56-C603-C49C71C490D6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{42420588-391A-AC56-C603-C49C71C490D6}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E575E0A-2CD3-9105-943C-9A3243CA9F5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E575E0A-2CD3-9105-943C-9A3243CA9F5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E575E0A-2CD3-9105-943C-9A3243CA9F5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E575E0A-2CD3-9105-943C-9A3243CA9F5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{110441C6-038A-1FE8-BDE5-0FF9395CE5A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{110441C6-038A-1FE8-BDE5-0FF9395CE5A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{110441C6-038A-1FE8-BDE5-0FF9395CE5A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{110441C6-038A-1FE8-BDE5-0FF9395CE5A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D176265-BF3D-8AF2-B287-6FCC8348FCE4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D176265-BF3D-8AF2-B287-6FCC8348FCE4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D176265-BF3D-8AF2-B287-6FCC8348FCE4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D176265-BF3D-8AF2-B287-6FCC8348FCE4}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5296185-0CBE-F914-1F65-8CA162AA72F6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5296185-0CBE-F914-1F65-8CA162AA72F6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5296185-0CBE-F914-1F65-8CA162AA72F6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5296185-0CBE-F914-1F65-8CA162AA72F6}.Release|Any CPU.Build.0 = Release|Any CPU
		{C07C6D7C-2660-AFB1-430B-9C256ECCF404}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C07C6D7C-2660-AFB1-430B-9C256ECCF404}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C07C6D7C-2660-AFB1-430B-9C256ECCF404}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C07C6D7C-2660-AFB1-430B-9C256ECCF404}.Release|Any CPU.Build.0 = Release|Any CPU
		{877D9F31-1CAB-1BEC-3911-A28404FD47EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{877D9F31-1CAB-1BEC-3911-A28404FD47EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{877D9F31-1CAB-1BEC-3911-A28404FD47EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{877D9F31-1CAB-1BEC-3911-A28404FD47EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{420B8032-D778-4B73-028E-483CE5060308}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{420B8032-D778-4B73-028E-483CE5060308}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{420B8032-D778-4B73-028E-483CE5060308}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{420B8032-D778-4B73-028E-483CE5060308}.Release|Any CPU.Build.0 = Release|Any CPU
		{186C9F35-36F5-D177-C774-40824D3C73FA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{186C9F35-36F5-D177-C774-40824D3C73FA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{186C9F35-36F5-D177-C774-40824D3C73FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{186C9F35-36F5-D177-C774-40824D3C73FA}.Release|Any CPU.Build.0 = Release|Any CPU
		{02E51364-A3E5-F85A-3B7E-47829A87B53A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02E51364-A3E5-F85A-3B7E-47829A87B53A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02E51364-A3E5-F85A-3B7E-47829A87B53A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02E51364-A3E5-F85A-3B7E-47829A87B53A}.Release|Any CPU.Build.0 = Release|Any CPU
		{82E29B65-EAF5-8036-392E-9A6644E599FA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82E29B65-EAF5-8036-392E-9A6644E599FA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82E29B65-EAF5-8036-392E-9A6644E599FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82E29B65-EAF5-8036-392E-9A6644E599FA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal

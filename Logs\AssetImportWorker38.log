Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker38
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker38.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [52572]  Target information:

Player connection [52572]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1569796760 [EditorId] 1569796760 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [52572] Host joined multi-casting on [***********:54997]...
Player connection [52572] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56468
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002344 seconds.
- Loaded All Assemblies, in  0.359 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 225 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.509 seconds
Domain Reload Profiling: 867ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (127ms)
		LoadAssemblies (150ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (124ms)
			TypeCache.Refresh (123ms)
				TypeCache.ScanAssembly (112ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (509ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (471ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (297ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (37ms)
			ProcessInitializeOnLoadAttributes (89ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 34.323 seconds
Refreshing native plugins compatible for Editor in 8.53 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.628 seconds
Domain Reload Profiling: 35949ms
	BeginReloadAssembly (128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (34125ms)
		LoadAssemblies (484ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33715ms)
			TypeCache.Refresh (209ms)
				TypeCache.ScanAssembly (186ms)
			BuildScriptInfoCaches (33372ms)
			ResolveRequiredComponents (112ms)
	FinalizeReload (1628ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1138ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (200ms)
			ProcessInitializeOnLoadAttributes (566ms)
			ProcessInitializeOnLoadMethodAttributes (339ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 4.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.3 MB). Loaded Objects now: 8010.
Memory consumption went from 184.8 MB to 177.5 MB.
Total: 17.540900 ms (FindLiveObjects: 0.821000 ms CreateObjectMapping: 1.061300 ms MarkObjects: 8.645500 ms  DeleteObjects: 7.008200 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7327 unused Assets / (6.4 MB). Loaded Objects now: 8011.
Memory consumption went from 152.6 MB to 146.2 MB.
Total: 36.159000 ms (FindLiveObjects: 0.804200 ms CreateObjectMapping: 0.587200 ms MarkObjects: 31.298000 ms  DeleteObjects: 3.466800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 12.405 seconds
Refreshing native plugins compatible for Editor in 1.70 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.893 seconds
Domain Reload Profiling: 13299ms
	BeginReloadAssembly (912ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (328ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (11128ms)
		LoadAssemblies (10587ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1294ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (1261ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (893ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (581ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (95ms)
			ProcessInitializeOnLoadAttributes (318ms)
			ProcessInitializeOnLoadMethodAttributes (148ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7347 unused Assets / (7.8 MB). Loaded Objects now: 8026.
Memory consumption went from 167.2 MB to 159.4 MB.
Total: 12.922300 ms (FindLiveObjects: 0.757100 ms CreateObjectMapping: 0.826200 ms MarkObjects: 5.866200 ms  DeleteObjects: 5.471300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.799 seconds
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.756 seconds
Domain Reload Profiling: 1556ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (534ms)
		LoadAssemblies (447ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (756ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (597ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (311ms)
			ProcessInitializeOnLoadMethodAttributes (147ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 3.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7347 unused Assets / (7.6 MB). Loaded Objects now: 8028.
Memory consumption went from 167.4 MB to 159.8 MB.
Total: 15.538100 ms (FindLiveObjects: 1.226500 ms CreateObjectMapping: 1.321200 ms MarkObjects: 7.370500 ms  DeleteObjects: 5.617400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.682 seconds
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 1389ms
	BeginReloadAssembly (168ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (450ms)
		LoadAssemblies (330ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (198ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (708ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (550ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (147ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 2.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7347 unused Assets / (7.6 MB). Loaded Objects now: 8030.
Memory consumption went from 167.4 MB to 159.8 MB.
Total: 13.282300 ms (FindLiveObjects: 0.700500 ms CreateObjectMapping: 1.120500 ms MarkObjects: 6.471600 ms  DeleteObjects: 4.987800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.658 seconds
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.763 seconds
Domain Reload Profiling: 1422ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (433ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (764ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (595ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (306ms)
			ProcessInitializeOnLoadMethodAttributes (153ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.5 MB). Loaded Objects now: 8033.
Memory consumption went from 167.4 MB to 160.0 MB.
Total: 13.202900 ms (FindLiveObjects: 0.755600 ms CreateObjectMapping: 1.075500 ms MarkObjects: 6.424500 ms  DeleteObjects: 4.945000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1650096.943086 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_SUV_02B.fbx
  artifactKey: Guid(2b7ee5c3bc7bb2c4ca103996005bc60c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_SUV_02B.fbx using Guid(2b7ee5c3bc7bb2c4ca103996005bc60c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '19bcd372750fd08e0fe642305f20123e') in 0.9151697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 72

========================================================================
Received Import Request.
  Time since last request: 0.006243 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_02A.fbx
  artifactKey: Guid(cb3724dfd5706e54aaeaf67055cbb0d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_02A.fbx using Guid(cb3724dfd5706e54aaeaf67055cbb0d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61f230ddde1fa9ed8fd2b60c273f8d97') in 0.0435029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.022159 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_04A.fbx
  artifactKey: Guid(fc1f5c5019827c7478210757d73ef26f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_04A.fbx using Guid(fc1f5c5019827c7478210757d73ef26f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5ce28343ac714dd86df4a02dee2550bc') in 0.0298498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.142115 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Clock_01A.fbx
  artifactKey: Guid(d4cd4867e5cb7ae439d891b378f48be1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Clock_01A.fbx using Guid(d4cd4867e5cb7ae439d891b378f48be1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7f63445fb408794c02424183f7684cfc') in 0.0458648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Umbrella_01B.fbx
  artifactKey: Guid(49270be61ed5e784ab1c0625e61725b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Umbrella_01B.fbx using Guid(49270be61ed5e784ab1c0625e61725b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e47b4dcd2011cdd4e4bef920e66af0a9') in 0.0255137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Umbrella_01C.fbx
  artifactKey: Guid(b92f4de5cc1c3154296c1e63f4e30395) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Umbrella_01C.fbx using Guid(b92f4de5cc1c3154296c1e63f4e30395) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab6398e6d95cac7b54b6a29dbb7e32bc') in 0.0283838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Stand_01A.fbx
  artifactKey: Guid(ee323c3d354074d40aa6e0979d80c979) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Stand_01A.fbx using Guid(ee323c3d354074d40aa6e0979d80c979) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa2deb108c09bad87b0b2fb2c619e213') in 0.0556962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_TV_Remote_01A.fbx
  artifactKey: Guid(05bb91124b2427546aa552e926afdc4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_TV_Remote_01A.fbx using Guid(05bb91124b2427546aa552e926afdc4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db2b78a0626e2d18cf5c7f17055464f6') in 0.0257756 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_03C.fbx
  artifactKey: Guid(865b8def05d504541b72ff728910a254) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_03C.fbx using Guid(865b8def05d504541b72ff728910a254) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b60b1e771ddaef18adc14099517ed484') in 0.143072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_01B.fbx
  artifactKey: Guid(71d6b32c7cb92084c8e6e75f55d078db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_01B.fbx using Guid(71d6b32c7cb92084c8e6e75f55d078db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5a8b4dc864b0c02a7a9de0d63f3ef83d') in 0.0394444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_03A.fbx
  artifactKey: Guid(d2ee1fc35cd4ced48923ef1ca73a5742) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_03A.fbx using Guid(d2ee1fc35cd4ced48923ef1ca73a5742) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c00f4126b6d30f2eb6abd98c21919fb') in 0.0359225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_01C.fbx
  artifactKey: Guid(800c2bc57c1eb6d4a90946cbf39ab4e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_01C.fbx using Guid(800c2bc57c1eb6d4a90946cbf39ab4e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '983f22d4da6d11a7429fde0176faf054') in 0.0386693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_04B.fbx
  artifactKey: Guid(5a823ac9a04da0f418d6a3a4ebcf3216) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_04B.fbx using Guid(5a823ac9a04da0f418d6a3a4ebcf3216) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b42bd4cebaf63646fede49f181445cb3') in 0.0377909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_03B.fbx
  artifactKey: Guid(c019953c9be67424fbdcb2331ba521d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_03B.fbx using Guid(c019953c9be67424fbdcb2331ba521d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '81b342ddf11596fe3f4c16228a275032') in 0.0364474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Trash_Can_02A.fbx
  artifactKey: Guid(6d7a6bb9009504c4ba67b89bbfc836d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Trash_Can_02A.fbx using Guid(6d7a6bb9009504c4ba67b89bbfc836d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '80b057b5462a107c740b537088651b1c') in 0.0342149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_01A.fbx
  artifactKey: Guid(987c81182f848794ca8cc3bd69f1e242) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Wall_Frame_01A.fbx using Guid(987c81182f848794ca8cc3bd69f1e242) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3d80ae3c169792caa78de2b45eb54d34') in 0.0242518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Umbrella_01A.fbx
  artifactKey: Guid(3716cdb8fac9f7d4bbbd2c2a50c2ea8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Umbrella_01A.fbx using Guid(3716cdb8fac9f7d4bbbd2c2a50c2ea8e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ef4eef885220731bc88daaaedb2d25f4') in 0.0294941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Tree_04C.fbx
  artifactKey: Guid(4c656d106d2a3d74d8cc5bbc6b6f5d14) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Tree_04C.fbx using Guid(4c656d106d2a3d74d8cc5bbc6b6f5d14) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd712b7f1e4164a35a1bd6cb132a6d8d') in 0.0418457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_SUV_02A.fbx
  artifactKey: Guid(4bf834fb5fb6eb04fb136c8932cacfe5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_SUV_02A.fbx using Guid(4bf834fb5fb6eb04fb136c8932cacfe5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '516ac7877ae3f8d4b67e464da76e747f') in 0.148261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 76

========================================================================
Received Import Request.
  Time since last request: 7.733436 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Metal_Chair_01A_MeshCollider.fbx
  artifactKey: Guid(469fbb762ec08a148b89e93c8c84f795) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Metal_Chair_01A_MeshCollider.fbx using Guid(469fbb762ec08a148b89e93c8c84f795) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e95761324864eb654a11496e2e79b2e5') in 0.9054595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_F.L_MeshCollider.fbx
  artifactKey: Guid(5e63013010ff2034fb3ea19f2c5a6971) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_F.L_MeshCollider.fbx using Guid(5e63013010ff2034fb3ea19f2c5a6971) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34aea7825995e4c173d6fa2e72b9d623') in 0.0493021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Body_MeshCollider.fbx
  artifactKey: Guid(8cc112dea4720fe489cbc6a7ca4cac83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Body_MeshCollider.fbx using Guid(8cc112dea4720fe489cbc6a7ca4cac83) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cab910ff7125e70087c451dba30e7792') in 0.0706715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.740320 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_R.L_MeshCollider.fbx
  artifactKey: Guid(efa255d3aceb3d14ebd9663a3ab5960c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Door_R.L_MeshCollider.fbx using Guid(efa255d3aceb3d14ebd9663a3ab5960c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50f2989b50c36615eedaf62e9b181f80') in 0.0260595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Trunk_MeshCollider.fbx
  artifactKey: Guid(fe6258dd9913f084889cdbf89de90fc9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Minivan_01A_Trunk_MeshCollider.fbx using Guid(fe6258dd9913f084889cdbf89de90fc9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dac67b125107ba5ce7c4c455c2961f0d') in 0.0263651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 2.538922 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Lounge_Chair_02A_MeshCollider.fbx
  artifactKey: Guid(275a57106867bef40b65a213d72b0a3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Lounge_Chair_02A_MeshCollider.fbx using Guid(275a57106867bef40b65a213d72b0a3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '65e3ba634d586218513ff08a171268f0') in 0.0235388 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 5.918402 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Lemonade_Stand_01A_MeshCollider.fbx
  artifactKey: Guid(d27fe9f2a1e0e4145b3017aeb2faf550) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Lemonade_Stand_01A_MeshCollider.fbx using Guid(d27fe9f2a1e0e4145b3017aeb2faf550) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b3399fb67cd612528fd7e4acdad9151') in 0.0268757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 12.752816 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Lawnmower_01A_MeshCollider.fbx
  artifactKey: Guid(49d33bdf440f888459acfed0f7d1e333) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Lawnmower_01A_MeshCollider.fbx using Guid(49d33bdf440f888459acfed0f7d1e333) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ac3d492120609db9aca94f8392247530') in 0.0423184 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.305883 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_Corner_01A.fbx
  artifactKey: Guid(2d8d9cfbceae3fd46aad29957059b725) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_Corner_01A.fbx using Guid(2d8d9cfbceae3fd46aad29957059b725) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a9b90f1e7d778df440941e92d162c0e') in 0.0800841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 1.455735 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_03B.fbx
  artifactKey: Guid(19ef9dc7a8397b846b916aecc90544e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_03B.fbx using Guid(19ef9dc7a8397b846b916aecc90544e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '946c05d8be373cb28f1def4591af7c8d') in 0.0372101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_05B.fbx
  artifactKey: Guid(0325ec927561bd34c8493c5894ff86b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_05B.fbx using Guid(0325ec927561bd34c8493c5894ff86b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e3d20f99525d038415491423276e8de') in 0.0254898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.337630 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_02B.fbx
  artifactKey: Guid(9667d97537761914da53fea01fe6ff50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_02B.fbx using Guid(9667d97537761914da53fea01fe6ff50) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e0865798ef88886fa7e6d378b39d977') in 0.0368585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_02A.fbx
  artifactKey: Guid(7117774bc4926cd449dced0bb1d996f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Top_02A.fbx using Guid(7117774bc4926cd449dced0bb1d996f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f673c53bf2fae73c1772d6a78e9f59d0') in 0.0380866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 2.124809 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Sink_01B.fbx
  artifactKey: Guid(78db93bf3b4f7414c92acc54ce2644a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_Sink_01B.fbx using Guid(78db93bf3b4f7414c92acc54ce2644a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '372a8f31e5ceed55ca99b865c94e0146') in 0.0477018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_End_01A.fbx
  artifactKey: Guid(eef5d688747a61347a610918d765cdc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_End_01A.fbx using Guid(eef5d688747a61347a610918d765cdc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3bf40c7fa97b3a2f4f41e8848672315c') in 0.0259117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 56.581370 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_04A.fbx
  artifactKey: Guid(09efb252b74abcc49b465811071f2f2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_04A.fbx using Guid(09efb252b74abcc49b465811071f2f2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f3b7bd5189ed7ec4d303a31fd015b532') in 0.0256967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.099042 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_03A.fbx
  artifactKey: Guid(00f6d47c3b2fd284abc80cb627a3b678) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_03A.fbx using Guid(00f6d47c3b2fd284abc80cb627a3b678) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f57c6c3688247d209787a539635e69b9') in 0.0552446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.259021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_02A.fbx
  artifactKey: Guid(f1f0675d08c54c844aa346295de00649) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_02A.fbx using Guid(f1f0675d08c54c844aa346295de00649) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8c6062b14f9ee6219de9276e2eb76496') in 0.035745 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.111048 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_01A.fbx
  artifactKey: Guid(a5e7b3440d684094295fe921b90850ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Kitchen_Cabinet_01A.fbx using Guid(a5e7b3440d684094295fe921b90850ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3f496062e51a5184b524132e8127b4a1') in 0.041697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Tiles_02B.fbx
  artifactKey: Guid(88a04ec8de5436d46887118c151dc31c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Tiles_02B.fbx using Guid(88a04ec8de5436d46887118c151dc31c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da7c5513e29d929604ce31ddecb3e60c') in 0.0238403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.057746 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_05B.fbx
  artifactKey: Guid(c39df6954abd4d148a7cf0d6acc22457) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_05B.fbx using Guid(c39df6954abd4d148a7cf0d6acc22457) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d53bf9f0c5ec920b480596b176192b0') in 0.0248191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Tiles_01A.fbx
  artifactKey: Guid(c9f781315e83f8b4e97e2a03df07c434) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Tiles_01A.fbx using Guid(c9f781315e83f8b4e97e2a03df07c434) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a6b0b1af3912010270388cae6a56ea21') in 0.0442107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.037003 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_02A.fbx
  artifactKey: Guid(1e22fc81956955944ac9e3fc70affd63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_02A.fbx using Guid(1e22fc81956955944ac9e3fc70affd63) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fb75b3b6c9eb3da2df4afd64489f9695') in 0.0242702 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_03B.fbx
  artifactKey: Guid(895b3fc2dd79cf44bbf41067e3d45c30) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_03B.fbx using Guid(895b3fc2dd79cf44bbf41067e3d45c30) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '57f23875da7815781d30514d5275f2b0') in 0.0333166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Interior_Wall_Regular_05A_MeshCollider.fbx
  artifactKey: Guid(d2e8f681659796d4a8a34ee1da03849e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Interior_Wall_Regular_05A_MeshCollider.fbx using Guid(d2e8f681659796d4a8a34ee1da03849e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1db9697f1dd75d5fe52784cc9192df69') in 0.0249174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_01B.fbx
  artifactKey: Guid(2b43f0a2aae75544b8cfd188aecd9c4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Short_01B.fbx using Guid(2b43f0a2aae75544b8cfd188aecd9c4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fca34ba6ead610e5c20b2bfb18168061') in 0.0220372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_04B.fbx
  artifactKey: Guid(50a259145344743449386bb874adc562) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_04B.fbx using Guid(50a259145344743449386bb874adc562) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a5ef28fe0c47b46bbb276e2faa42e3a6') in 0.0372111 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.050504 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_02A.fbx
  artifactKey: Guid(64663c76baef5bb409d0b4f8a3db3f63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Interior_Wall_Regular_02A.fbx using Guid(64663c76baef5bb409d0b4f8a3db3f63) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '106afd69dbbc7a9dacf924ef2b98ebb3') in 0.023984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.021613 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_04B.fbx
  artifactKey: Guid(7eb7c17a5ceac8c49985dc202037e8b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_04B.fbx using Guid(7eb7c17a5ceac8c49985dc202037e8b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4805603a2081d30fdbbcce016cdf7729') in 0.0619706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.023162 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_03A.fbx
  artifactKey: Guid(21023ed7989b68e4e88e6ed96235f658) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_03A.fbx using Guid(21023ed7989b68e4e88e6ed96235f658) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '973b4bcd4fbc442389d84bb1afcd6b77') in 0.0517901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.130404 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_02B.fbx
  artifactKey: Guid(1b02245b317e71f45a25dabe5c798e18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_02B.fbx using Guid(1b02245b317e71f45a25dabe5c798e18) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9d8b1f36a0afff057766f4dd8c01d9ba') in 0.0455687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.042015 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_01A.fbx
  artifactKey: Guid(9db9f1162961d4845856dd0cda6601a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Special_01A.fbx using Guid(9db9f1162961d4845856dd0cda6601a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6e9afe1375bded69222c256e30605fa') in 0.0436519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.269910 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_15A.fbx
  artifactKey: Guid(da78bdee2b89dd84d9f77565cc26fee4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_15A.fbx using Guid(da78bdee2b89dd84d9f77565cc26fee4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8fe35b8ab63cefa01211968c7e5fd710') in 0.0486291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_13B.fbx
  artifactKey: Guid(bd9025a15e5c79d4aa3b2a5979f56f25) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_13B.fbx using Guid(bd9025a15e5c79d4aa3b2a5979f56f25) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7cee9a3052e1fc5e6c1c4d36a94aa00e') in 0.0410683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_12A.fbx
  artifactKey: Guid(c958ba8481a73684284390dd67552fb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_12A.fbx using Guid(c958ba8481a73684284390dd67552fb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ebb6afc882272e05ef3ec71a40fbac3') in 0.0310415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_10B.fbx
  artifactKey: Guid(713f49a0c1797084d89476325fcba5c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_10B.fbx using Guid(713f49a0c1797084d89476325fcba5c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '41136f377320f002dce79259bbec0878') in 0.052746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.217701 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_09B.fbx
  artifactKey: Guid(ce0b945e94c18ac49b645722d5ee7948) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_09B.fbx using Guid(ce0b945e94c18ac49b645722d5ee7948) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1fafb31a3107e8f4b263c7120ad75551') in 0.0420855 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_08B.fbx
  artifactKey: Guid(561236bb64ff26e42b84fd6c0a1dae22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_08B.fbx using Guid(561236bb64ff26e42b84fd6c0a1dae22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04696a23b8e307181bd3e6ecc3cad175') in 0.041226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_06B.fbx
  artifactKey: Guid(7eed26383093cb94ebfb196c2a22a33f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_06B.fbx using Guid(7eed26383093cb94ebfb196c2a22a33f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f55dce7a6d7e387097c3b6ae2261f6d') in 0.037599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.148078 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_04B.fbx
  artifactKey: Guid(472eca9ffaab01847911fbe38dffe6e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_04B.fbx using Guid(472eca9ffaab01847911fbe38dffe6e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '186ee7a5093b8247a5d3089b024a09d9') in 0.0461618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_03A.fbx
  artifactKey: Guid(b5eaabde4b5e4fb4b866a00cc49932d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_03A.fbx using Guid(b5eaabde4b5e4fb4b866a00cc49932d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2567ae11f1a593640e01f7d2854cb03b') in 0.0403808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Short_03A_MeshCollider.fbx
  artifactKey: Guid(10ea66efd2ff9174c96c0c7db5dfd93a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Short_03A_MeshCollider.fbx using Guid(10ea66efd2ff9174c96c0c7db5dfd93a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e24fd0a24b8bee702a8cfd611f268519') in 0.0284535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_01B.fbx
  artifactKey: Guid(4496abaf9cc9ec648a708125ec1ab58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Short_01B.fbx using Guid(4496abaf9cc9ec648a708125ec1ab58f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd9a74ada1db3412024f3ae5168058255') in 0.0430409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_06B.fbx
  artifactKey: Guid(fbd3cff6265ba7e48a4341a3a7a6eec9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_06B.fbx using Guid(fbd3cff6265ba7e48a4341a3a7a6eec9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b77c401fc4a50a72f2593a3d90da527e') in 0.0251473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.032868 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_03A_End.fbx
  artifactKey: Guid(96484bfd71bca634892ea2544eb02cf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_03A_End.fbx using Guid(96484bfd71bca634892ea2544eb02cf7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9db735246f7d044f3e6d80eb86a1cf5') in 0.0225978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_04A.fbx
  artifactKey: Guid(a02d0077395e6a349a86e75233dc4aa7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_04A.fbx using Guid(a02d0077395e6a349a86e75233dc4aa7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '24eb5a8acc78bef715b68efb45fb172d') in 0.0288272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Roof_05A_MeshCollider.fbx
  artifactKey: Guid(1a701f375df79fd41a7668b70492ba56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Roof_05A_MeshCollider.fbx using Guid(1a701f375df79fd41a7668b70492ba56) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '92e5e7491dafe64e829347bdc994b06b') in 0.0250411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.015661 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_02A.fbx
  artifactKey: Guid(6d42d60446cd0b8478162b232876663b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_02A.fbx using Guid(6d42d60446cd0b8478162b232876663b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '181c3d2e5dc77a5609a88423dffa1b8c') in 0.0257331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_02B_End.fbx
  artifactKey: Guid(5cabd328d44bfe24b9139a72b018da19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_02B_End.fbx using Guid(5cabd328d44bfe24b9139a72b018da19) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5076a588ea228d81e7f441c42ac4ec9') in 0.0303167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_01A_End.fbx
  artifactKey: Guid(513ab7d96a60aee458d72b905a5e47f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Roof_01A_End.fbx using Guid(513ab7d96a60aee458d72b905a5e47f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '64884c89256eebe9d0303919b623c1f1') in 0.0278697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.182202 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_13B.fbx
  artifactKey: Guid(c5d95f60a9f520f4bbe706092717a1bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_13B.fbx using Guid(c5d95f60a9f520f4bbe706092717a1bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62bf1e9a16c6d3848c5c1d9dc2210de8') in 0.0360829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_11A.fbx
  artifactKey: Guid(42c0dcbf71a1229499cb6927412364f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_11A.fbx using Guid(42c0dcbf71a1229499cb6927412364f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3bab63ce8d3977e39d0d16c27d3746ee') in 0.0502474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.028773 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_09B.fbx
  artifactKey: Guid(7c6adc9608483d64d959ad9d830cc3cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_09B.fbx using Guid(7c6adc9608483d64d959ad9d830cc3cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4bf1fe4a997a7183916a31dfdb95413b') in 0.0536292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.075375 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_08B.fbx
  artifactKey: Guid(dc905328e83c2f542a6674259c0ba50e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_08B.fbx using Guid(dc905328e83c2f542a6674259c0ba50e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3950598df07cc07c737ec75860037961') in 0.0352333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.020640 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Regular_07A_MeshCollider.fbx
  artifactKey: Guid(fdddf2a8ccfb58a4985bd50700588525) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Wall_Regular_07A_MeshCollider.fbx using Guid(fdddf2a8ccfb58a4985bd50700588525) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f16b0c6ee40394c7e8df61cc839d73c') in 0.0226208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.019256 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_06A.fbx
  artifactKey: Guid(d1e521659630d62478194f0a173ed07a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_06A.fbx using Guid(d1e521659630d62478194f0a173ed07a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '147e5165995990a0923ac3a764aeebc5') in 0.0316334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.012457 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_04A.fbx
  artifactKey: Guid(e0e3bc403617962428a5c3c32f5a8c83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_04A.fbx using Guid(e0e3bc403617962428a5c3c32f5a8c83) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6c4d0345b90cda4df894965e960b891d') in 0.038891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.001016 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_03B.fbx
  artifactKey: Guid(f06e39aa5d324194bbe5b8183d9482f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_03B.fbx using Guid(f06e39aa5d324194bbe5b8183d9482f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fe6bbcb736cfb4f1bdbd75003297fb31') in 0.04204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.791133 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_02B.fbx
  artifactKey: Guid(9ce9f1bae7b83c34bafb14d5e3fccf9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_02B.fbx using Guid(9ce9f1bae7b83c34bafb14d5e3fccf9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '863e8ae579c58581dd461d064e99a2bb') in 0.0393945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Glass_01A.fbx
  artifactKey: Guid(8d909b10c6daa0748966fcefda1401fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Glass_01A.fbx using Guid(8d909b10c6daa0748966fcefda1401fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc7c8968cc8f0dc5a9455689a1d68ede') in 0.0231909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Rail_02B.fbx
  artifactKey: Guid(57ceef0bb595c0c4986222035fb9ed60) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Rail_02B.fbx using Guid(57ceef0bb595c0c4986222035fb9ed60) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dad187271291cd78b532081b8ce9a4ef') in 0.0247023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_02A.fbx
  artifactKey: Guid(dead8e72fd2fde54a83267400f0dd6aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Wall_Regular_02A.fbx using Guid(dead8e72fd2fde54a83267400f0dd6aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a2a9567a12b00ecdc222172d5b6ce57c') in 0.0392518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01B.L.fbx
  artifactKey: Guid(95d1c67da4757294fa3f1dc7cb2da002) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01B.L.fbx using Guid(95d1c67da4757294fa3f1dc7cb2da002) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd203424ac34bb00dbbaa3fe1a7a47f72') in 0.0224293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01B.R.fbx
  artifactKey: Guid(deaeb38be006eb1488462aae06f1e8f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_01B.R.fbx using Guid(deaeb38be006eb1488462aae06f1e8f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7224cefee8edae64ef55fb29cc92c84f') in 0.0251058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_Corner_Rails_01A.L_MeshCollider.fbx
  artifactKey: Guid(3b316d19b4faa994aa6c7e80e1fd9155) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_Corner_Rails_01A.L_MeshCollider.fbx using Guid(3b316d19b4faa994aa6c7e80e1fd9155) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34b7546b662b956e9cc58ea4ec8ce696') in 0.0266664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Rails_01B.fbx
  artifactKey: Guid(80ac0444df281f14d9fb0b8b44bf9656) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Rails_01B.fbx using Guid(80ac0444df281f14d9fb0b8b44bf9656) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e35cf168de05e9c066539a91042f05e7') in 0.0272064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_Rails_03A_MeshCollider.fbx
  artifactKey: Guid(91ce36e58b225144d90d346ad0cd72a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Stairs_Rails_03A_MeshCollider.fbx using Guid(91ce36e58b225144d90d346ad0cd72a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '76028f417765ed13389880e9f100f1d4') in 0.0272082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_Rails_01A.L.fbx
  artifactKey: Guid(76d6df14fabe96243b9a21eda88b71b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_Rails_01A.L.fbx using Guid(76d6df14fabe96243b9a21eda88b71b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd8e8d89fdff891913066fc9782087e77') in 0.0200214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_04A_1.fbx
  artifactKey: Guid(128de5e445b4ae44ca0182c738d265f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_04A_1.fbx using Guid(128de5e445b4ae44ca0182c738d265f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0bdfddadbe991c72a027568f543b82d7') in 0.0246144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_04C_3.fbx
  artifactKey: Guid(11a7f92f53abe7a41938ae90c809ea08) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_04C_3.fbx using Guid(11a7f92f53abe7a41938ae90c809ea08) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '56a14b3fa44beed72262d857ff3b4418') in 0.025113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_05A_End.fbx
  artifactKey: Guid(9397cc340390fe84caefc88f3f3ec57b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_05A_End.fbx using Guid(9397cc340390fe84caefc88f3f3ec57b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '855fc2c3fcd1ec8577d5c6e5a1f82437') in 0.0232392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Roof_06A_End_MeshCollider.fbx
  artifactKey: Guid(2e2dcbfc36d60ba4786522df7bb021a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_House_Roof_06A_End_MeshCollider.fbx using Guid(2e2dcbfc36d60ba4786522df7bb021a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '361807520fca8dd3d1a8628c1a26f494') in 0.0230202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_01A.fbx
  artifactKey: Guid(fb53ef5a61ff6664eb77663a72052a3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_01A.fbx using Guid(fb53ef5a61ff6664eb77663a72052a3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '91fce5d2493e96245766ef653523c404') in 0.0252595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_06A.fbx
  artifactKey: Guid(47222904b7be9004b9475e55e5e13bee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_06A.fbx using Guid(47222904b7be9004b9475e55e5e13bee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b6f8e8b6729916f9e8c39a623c7e655') in 0.0299421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_04A_2.fbx
  artifactKey: Guid(4dde07df9fbf6c14db12cdd03ae800f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_04A_2.fbx using Guid(4dde07df9fbf6c14db12cdd03ae800f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8788090fd990435c8a0af53dc21d2969') in 0.0367076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_Rails_01A.R.fbx
  artifactKey: Guid(98e0f2f12c5be644789a3143eddf73ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Stairs_Corner_Rails_01A.R.fbx using Guid(98e0f2f12c5be644789a3143eddf73ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4fe269831b5f83a7b5c2fcc52b3f5fc0') in 0.0382311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.391159 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_03B_1.fbx
  artifactKey: Guid(bbe86610cda236e4cb8a7f588d88c033) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_03B_1.fbx using Guid(bbe86610cda236e4cb8a7f588d88c033) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dff8d3a02406df63ee084d82a4ab3ea8') in 0.026216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_03C_2.fbx
  artifactKey: Guid(c5667406aa0251242ac05689b9808bff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_03C_2.fbx using Guid(c5667406aa0251242ac05689b9808bff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '93be5f56c1820debb429952089d076f8') in 0.0281015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.009602 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04B_Open_Center.fbx
  artifactKey: Guid(c00eb2fab9c8eca4896a7a1048db6630) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04B_Open_Center.fbx using Guid(c00eb2fab9c8eca4896a7a1048db6630) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4dde525a2c20e111ef3b9844ec687107') in 0.0218604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04C_Open_Center.fbx
  artifactKey: Guid(51f8c92f11b8c754c9ba262369184b0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_04C_Open_Center.fbx using Guid(51f8c92f11b8c754c9ba262369184b0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf9d534a92edac22e861c8aef05fe272') in 0.0263195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05A_Open_Corner.fbx
  artifactKey: Guid(442ac9e08d6f5204694d8bbf60be88fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05A_Open_Corner.fbx using Guid(442ac9e08d6f5204694d8bbf60be88fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '338eec3e5ec97b3b804932e2de8a1d58') in 0.027047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05B_Open_Side.fbx
  artifactKey: Guid(90bbf2a81aee60346b94c94805414437) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_05B_Open_Side.fbx using Guid(90bbf2a81aee60346b94c94805414437) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b533548676a3d4600f6c9ca830c44c0') in 0.0252346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Rail_03B.fbx
  artifactKey: Guid(9b9efe0a75cb76b4e9d46ca83a22bc53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Rail_03B.fbx using Guid(9b9efe0a75cb76b4e9d46ca83a22bc53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '38df424cf19c96409cedc009d6f65f55') in 0.0246743 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_01A_2.fbx
  artifactKey: Guid(f1b9bac89b2856b4bac2a2d843c2ed12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_01A_2.fbx using Guid(f1b9bac89b2856b4bac2a2d843c2ed12) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '931671cd94d501e1543d4459494e816a') in 0.0272118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Roof_01B_1.fbx
  artifactKey: Guid(27004159a52659144a4739f20c139ded) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Roof_01B_1.fbx using Guid(27004159a52659144a4739f20c139ded) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ebbbb4c64327c5fbf01160196cad2e75') in 0.0251961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01A_Open_Side.fbx
  artifactKey: Guid(f34627e20e974a645813e0d10c796fc4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01A_Open_Side.fbx using Guid(f34627e20e974a645813e0d10c796fc4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b2a27b7b7f8b2b8d172f18a0db1d7b66') in 0.026285 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01B_Open_Side.fbx
  artifactKey: Guid(012012c7e8de3c34dacdbc31374f0aeb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01B_Open_Side.fbx using Guid(012012c7e8de3c34dacdbc31374f0aeb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18eca4ac3c52e980ace50e8fda1554ed') in 0.0266131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02A_Open_Corner.fbx
  artifactKey: Guid(7c274dcb65199cc46af3f8a1a680618a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02A_Open_Corner.fbx using Guid(7c274dcb65199cc46af3f8a1a680618a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '900dde03cd7d4849474a4eebcc3498ad') in 0.0268716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03A.fbx
  artifactKey: Guid(7a90da7dc133eb84c90867e6226678fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03A.fbx using Guid(7a90da7dc133eb84c90867e6226678fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '87ce55d9ea91f5927396bab1e1a28dc7') in 0.0252366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03C_Open_Center.fbx
  artifactKey: Guid(e97dcc173c9d71c4abbde46e590d96a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03C_Open_Center.fbx using Guid(e97dcc173c9d71c4abbde46e590d96a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '614ec92a598e2e5fb0ff2542a291a88b') in 0.0277934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03A_Open_Center.fbx
  artifactKey: Guid(a65adcd5af17b1a46871b3bde7e9de45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03A_Open_Center.fbx using Guid(a65adcd5af17b1a46871b3bde7e9de45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9287ad4ccb1e1c508c5b1503ab93dbc4') in 0.0261662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01C_Open_Center.fbx
  artifactKey: Guid(daa659d185f3cc442bd57b1f943cef99) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01C_Open_Center.fbx using Guid(daa659d185f3cc442bd57b1f943cef99) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '70ba87c3d4974ef27e0bc234ef2e32a4') in 0.0258303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_03A.fbx
  artifactKey: Guid(cc5c1084c34b29e49bfb631e2e655be5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_03A.fbx using Guid(cc5c1084c34b29e49bfb631e2e655be5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '407456f13aa6ccb368b3f44016abe414') in 0.0275015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_03B.fbx
  artifactKey: Guid(e1be5f808bc14a845a352cb3203ddf0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Regular_03B.fbx using Guid(e1be5f808bc14a845a352cb3203ddf0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d48c9772a18559ae14e7f30c4fbaed6') in 0.0246006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01A.fbx
  artifactKey: Guid(49f7e3ec701cc6048aa20d7ede45007e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_01A.fbx using Guid(49f7e3ec701cc6048aa20d7ede45007e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5fe5df2462204f0bd04276e69fd0e02a') in 0.0275193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02A.fbx
  artifactKey: Guid(7e8dffde74ef4704d9f181ef83a9685a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_02A.fbx using Guid(7e8dffde74ef4704d9f181ef83a9685a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7d08ef8d547aa1ec423480ccbc778070') in 0.0303226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03A_Open_Side.fbx
  artifactKey: Guid(5a8cd207e92e9dd4cb37fcf714ac6856) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Floor_Small_03A_Open_Side.fbx using Guid(5a8cd207e92e9dd4cb37fcf714ac6856) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cd827cad5d4a310aa208d80773bac770') in 0.0284187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Pole_01A.fbx
  artifactKey: Guid(cc0bf3bf6b957124d8a4a0ebc5f2f56d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Pole_01A.fbx using Guid(cc0bf3bf6b957124d8a4a0ebc5f2f56d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '63b34a4cfba06ea2869453d6c6b4d41e') in 0.0266253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.609857 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Grill_01A.fbx
  artifactKey: Guid(e1636339c824c7549b7f712f0812bfff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Grill_01A.fbx using Guid(e1636339c824c7549b7f712f0812bfff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '765d38112b2a66fac964e9778321593a') in 0.0336774 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_House_Chimney_01A.fbx
  artifactKey: Guid(05067736b60e85143a6fd632dc8fa08a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_House_Chimney_01A.fbx using Guid(05067736b60e85143a6fd632dc8fa08a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c286ba8bbcfe8fab181e6e60a7e81e73') in 0.0328037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Garbage_Truck_01A_Body_MeshCollider.fbx
  artifactKey: Guid(33f4f20b98e235f4899f3fc66a000b01) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Garbage_Truck_01A_Body_MeshCollider.fbx using Guid(33f4f20b98e235f4899f3fc66a000b01) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '52ab71a6e9e475a877d35ced08fdea93') in 0.0228602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Garbage_Truck_01A_Door.R_MeshCollider.fbx
  artifactKey: Guid(a42eb072984176b4095f81aa9c214619) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Garbage_Truck_01A_Door.R_MeshCollider.fbx using Guid(a42eb072984176b4095f81aa9c214619) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45daf51435abe749041dfcc80aff8ff4') in 0.0206763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Garden_Wall_Short_01A_Gate_MeshCollider.fbx
  artifactKey: Guid(418b50974ebfe944fbb3a85a754ed0fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Garden_Wall_Short_01A_Gate_MeshCollider.fbx using Guid(418b50974ebfe944fbb3a85a754ed0fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed88f5a6d037a5cd2c7b6262e5dea989') in 0.0232869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_04A.fbx
  artifactKey: Guid(67537542d9ba23b4d80b224b06401ac0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_04A.fbx using Guid(67537542d9ba23b4d80b224b06401ac0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '540b21632be583c7e18d99b7edd06002') in 0.0279195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Garden_Ceiling_Small_01B.fbx
  artifactKey: Guid(f190e2410b1808646a21abdd10544c6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Garden_Ceiling_Small_01B.fbx using Guid(f190e2410b1808646a21abdd10544c6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '732e5b4d6f0892c796d442f49c0f07a4') in 0.0244022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Circular_01A.fbx
  artifactKey: Guid(9a68ffb6afa332d45b160bf10428a4d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Circular_01A.fbx using Guid(9a68ffb6afa332d45b160bf10428a4d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25420d87507bec195fc96f98f208b6ed') in 0.0249642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Green_01A.fbx
  artifactKey: Guid(279860712caa340489da7508637910bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Green_01A.fbx using Guid(279860712caa340489da7508637910bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c660765fb542b5a7b185b03c554fb616') in 0.0316152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02A.fbx
  artifactKey: Guid(4d9637acb1942144e98df90b66602e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_02A.fbx using Guid(4d9637acb1942144e98df90b66602e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0aa37702672fa4de6fe305588e990029') in 0.0284227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Green_01B.fbx
  artifactKey: Guid(a12ff3c16828c104ba5e2e5186fc82b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Green_01B.fbx using Guid(a12ff3c16828c104ba5e2e5186fc82b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1543d4107c7bcce66edd360e2fd055ac') in 0.0296675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_01A.fbx
  artifactKey: Guid(e32469a406984c54f83b84f2c5f67a67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Fence_Wood_01A.fbx using Guid(e32469a406984c54f83b84f2c5f67a67) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf91a759314d300767bb77ebec04f960') in 0.025338 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Dryer_01A.fbx
  artifactKey: Guid(8fb6122b8fdd1e340b100bd5abee3d2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Dryer_01A.fbx using Guid(8fb6122b8fdd1e340b100bd5abee3d2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9894fe9cb9e4667f1d819d7288ae9402') in 0.0257964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.011250 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01A.fbx
  artifactKey: Guid(111e99c30f5e4e74ba5d91a071fac7e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01A.fbx using Guid(111e99c30f5e4e74ba5d91a071fac7e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c18964eda7ada4ca5e6ea0958b4632a') in 0.0260051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01B.L.fbx
  artifactKey: Guid(076eebc9583b02f428b2fa8001d0328d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01B.L.fbx using Guid(076eebc9583b02f428b2fa8001d0328d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '911d2766d837de30601a8e7e63c2bcbe') in 0.0230266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01C.fbx
  artifactKey: Guid(9d8ca9dee28bac94da4f22460cff5f32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01C.fbx using Guid(9d8ca9dee28bac94da4f22460cff5f32) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '800127ec7a599faee7a63a0732589af5') in 0.027217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01D_Short.fbx
  artifactKey: Guid(3c0a4cde49875c740adb2a148599dc3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01D_Short.fbx using Guid(3c0a4cde49875c740adb2a148599dc3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a07e260d711105620356d988e265712a') in 0.0264611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_Support_01A.fbx
  artifactKey: Guid(98bedb4fb214cb841983fe836bfed19d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_Support_01A.fbx using Guid(98bedb4fb214cb841983fe836bfed19d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4f6f4816babfa30e4582c4b995942583') in 0.0271778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Deck_02A.fbx
  artifactKey: Guid(d12520c35246d354080238c710463109) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Deck_02A.fbx using Guid(d12520c35246d354080238c710463109) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7fe80c6e367f571e8214bec9e9837b1b') in 0.0393554 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Deck_01B.fbx
  artifactKey: Guid(499877474c6828e4c98b7374195d8b08) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Deck_01B.fbx using Guid(499877474c6828e4c98b7374195d8b08) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '725981c1d24818fd04740d1444019306') in 0.0399245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_Support_01B.fbx
  artifactKey: Guid(979c7bde1f040d548b47b25d1dc6a33b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_Support_01B.fbx using Guid(979c7bde1f040d548b47b25d1dc6a33b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '79bf0d3fb0e2ada2b442de9ce9968f19') in 0.0250537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Concrete_Column_02A.fbx
  artifactKey: Guid(5ee9ed3f9d77ca94e93b284e15ef85d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Concrete_Column_02A.fbx using Guid(5ee9ed3f9d77ca94e93b284e15ef85d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fca696f0eedb737d58e6174a151ccc87') in 0.0241259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Couch_02B.fbx
  artifactKey: Guid(914687a36897e6247827a7694a4846da) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Couch_02B.fbx using Guid(914687a36897e6247827a7694a4846da) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b277710a1fff32d8262c5acd66d07343') in 0.0284762 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curb_Straight_01A.fbx
  artifactKey: Guid(91bfbb0c79c001b4ba2c891db53567e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curb_Straight_01A.fbx using Guid(91bfbb0c79c001b4ba2c891db53567e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '516ee750aa51d6d4bc9aaabfc28446a8') in 0.0256118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Chandelier_01A.fbx
  artifactKey: Guid(c490c3aa0e391b24c9e54055cd66e91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Chandelier_01A.fbx using Guid(c490c3aa0e391b24c9e54055cd66e91d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '68554aa92ac382aa3158f5c93615a190') in 0.0375192 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Table_01A.fbx
  artifactKey: Guid(76336f02556032e46819587ec590c8b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Table_01A.fbx using Guid(76336f02556032e46819587ec590c8b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ecfbba9a827f9a4930e97a6c4193f22f') in 0.0263076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Coffee_Pot_01A.fbx
  artifactKey: Guid(feb8da4842801f44598ea5b97a60e2a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Coffee_Pot_01A.fbx using Guid(feb8da4842801f44598ea5b97a60e2a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84180bb0771a9fb2e13d3f03be645786') in 0.027902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Curb_Corner_01A_MeshCollider.fbx
  artifactKey: Guid(dd89064ddc2e4b2479de8e3cf9a94cd5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Curb_Corner_01A_MeshCollider.fbx using Guid(dd89064ddc2e4b2479de8e3cf9a94cd5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc9256e8cdc71148e13c04a0cbc6fb2c') in 0.0233479 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Curtain_01E.R.fbx
  artifactKey: Guid(26ded9bea5556fe4c8e65d81bc29ff04) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Curtain_01E.R.fbx using Guid(26ded9bea5556fe4c8e65d81bc29ff04) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '99cffae21308e39a54fb55a15c5c92e3') in 0.0335524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.106005 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Deck_Steps_02A.fbx
  artifactKey: Guid(c3f6312e058fa6f4d89e3d4a1029536c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Deck_Steps_02A.fbx using Guid(c3f6312e058fa6f4d89e3d4a1029536c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8c055b7dcb55ec976b1069fafafa6fa8') in 0.0240423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.194372 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Dresser_01A.fbx
  artifactKey: Guid(cea903fbbf56a8d49b5e09598afff9be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Dresser_01A.fbx using Guid(cea903fbbf56a8d49b5e09598afff9be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5bd1963057a209070402fb4bf15e64f1') in 0.0573464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.181223 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_01C.fbx
  artifactKey: Guid(16593299a59183448aeb40e65f9926b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_01C.fbx using Guid(16593299a59183448aeb40e65f9926b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab1d07c03dcad9927f5c07a55408ac23') in 0.0248444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_02D.fbx
  artifactKey: Guid(ffa280a9d0e67e94ab9c1a1b96c2dd3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_02D.fbx using Guid(ffa280a9d0e67e94ab9c1a1b96c2dd3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f987ebe52d5ad0d7e0f578d55571836e') in 0.0250431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_03A.fbx
  artifactKey: Guid(f68a03e8b8642d0478bb344b0fb5a961) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_03A.fbx using Guid(f68a03e8b8642d0478bb344b0fb5a961) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37854ee67699bdf5f5f14ad2796c149c') in 0.0276589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Toon Suburban Pack/Models/TSP_Flower_03E.fbx
  artifactKey: Guid(4f7e3f917dbeb3f4c97ac86957eb336d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/TSP_Flower_03E.fbx using Guid(4f7e3f917dbeb3f4c97ac86957eb336d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e581bf7ae70e5287f26d03161d4495ca') in 0.0286065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 6.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7329 unused Assets / (7.9 MB). Loaded Objects now: 8183.
Memory consumption went from 184.7 MB to 176.8 MB.
Total: 28.619000 ms (FindLiveObjects: 1.503700 ms CreateObjectMapping: 1.341100 ms MarkObjects: 19.492900 ms  DeleteObjects: 6.279400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.797 seconds
Refreshing native plugins compatible for Editor in 2.26 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.739 seconds
Domain Reload Profiling: 1537ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (523ms)
		LoadAssemblies (398ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (740ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (573ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (157ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 2.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.2 MB). Loaded Objects now: 8071.
Memory consumption went from 180.9 MB to 173.7 MB.
Total: 13.378600 ms (FindLiveObjects: 0.827500 ms CreateObjectMapping: 1.023800 ms MarkObjects: 6.680300 ms  DeleteObjects: 4.844700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 490.921722 seconds.
  path: Assets/Mirror/Examples/Billiards/Table/BilliardTable Model.obj
  artifactKey: Guid(25f03344dfdd844f88e89487c558fe35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mirror/Examples/Billiards/Table/BilliardTable Model.obj using Guid(25f03344dfdd844f88e89487c558fe35) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e296ac507393e920e4c46e081797cf9') in 0.5484839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Models/Colliders/TSP_Armchair_02A_MeshCollider.fbx
  artifactKey: Guid(422a0fd33cdc3654ba6b9f98aca76347) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Models/Colliders/TSP_Armchair_02A_MeshCollider.fbx using Guid(422a0fd33cdc3654ba6b9f98aca76347) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '756bf63c4612f0f1f3733c036c4bb068') in 0.0259358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Mirror/Examples/_Common/RobotKyle/Models/Robot Kyle.fbx
  artifactKey: Guid(320b1c2af77554f99a1658df4a6d3d5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mirror/Examples/_Common/RobotKyle/Models/Robot Kyle.fbx using Guid(320b1c2af77554f99a1658df4a6d3d5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'be7a4c5eafff231b6a59737edc2d6c9f') in 0.231953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 111

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
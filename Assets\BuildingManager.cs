using UnityEngine;
using System.Collections.Generic;
using System.Linq;

[System.Serializable]
public class BuildingData
{
    public string id;
    public string buildingName;
    public string buildingType;
    public int currentLevel;
    public int maxLevel;
    public Vector3 position;
    public Vector3 rotation;
    public Vector3 scale;
    public bool isActive;
    public string description;
    public Dictionary<string, object> customProperties;

    public BuildingData()
    {
        id = System.Guid.NewGuid().ToString();
        buildingName = "New Building";
        buildingType = "Default";
        currentLevel = 0;
        maxLevel = 1;
        position = Vector3.zero;
        rotation = Vector3.zero;
        scale = Vector3.one;
        isActive = true;
        description = "";
        customProperties = new Dictionary<string, object>();
    }

    public BuildingData(Building building)
    {
        id = System.Guid.NewGuid().ToString();
        buildingName = building.name;
        buildingType = "Default";
        currentLevel = building.level;
        maxLevel = building.bankLevels?.Length ?? 1;
        position = building.transform.position;
        rotation = building.transform.eulerAngles;
        scale = building.transform.localScale;
        isActive = building.gameObject.activeSelf;
        description = "";
        customProperties = new Dictionary<string, object>();
    }
}

public class BuildingManager : MonoBehaviour
{
    [Header("Building Management")]
    public Building[] buildings;

    [Header("Building Data Storage")]
    [SerializeField] private List<BuildingData> buildingDataList = new List<BuildingData>();

    [Header("Events")]
    public System.Action<BuildingData> OnBuildingAdded;
    public System.Action<BuildingData> OnBuildingRemoved;
    public System.Action<BuildingData> OnBuildingUpdated;
    public System.Action OnBuildingsRefreshed;

    private Dictionary<string, Building> buildingLookup = new Dictionary<string, Building>();
    private Dictionary<Building, string> reverseLookup = new Dictionary<Building, string>();

    void Start()
    {
        RefreshBuildings();
    }

    /// <summary>
    /// Refreshes the building arrays and data from child objects
    /// </summary>
    public void RefreshBuildings()
    {
        // Clear existing lookups
        buildingLookup.Clear();
        reverseLookup.Clear();

        // Get all building components from children
        buildings = GetComponentsInChildren<Building>();

        // Update or create building data for each building
        foreach (Building building in buildings)
        {
            string buildingId = GetOrCreateBuildingId(building);
            BuildingData existingData = buildingDataList.FirstOrDefault(bd => bd.id == buildingId);

            if (existingData == null)
            {
                // Create new building data
                BuildingData newData = new BuildingData(building);
                buildingDataList.Add(newData);
                buildingLookup[newData.id] = building;
                reverseLookup[building] = newData.id;
            }
            else
            {
                // Update existing data with current building state
                UpdateBuildingDataFromBuilding(existingData, building);
                buildingLookup[existingData.id] = building;
                reverseLookup[building] = existingData.id;
            }
        }

        // Remove data for buildings that no longer exist
        buildingDataList.RemoveAll(bd => !buildingLookup.ContainsKey(bd.id));

        OnBuildingsRefreshed?.Invoke();
    }

    /// <summary>
    /// Gets or creates a unique ID for a building
    /// </summary>
    private string GetOrCreateBuildingId(Building building)
    {
        // Try to find existing ID in reverse lookup
        if (reverseLookup.ContainsKey(building))
        {
            return reverseLookup[building];
        }

        // Check if building has a BuildingID component
        BuildingID idComponent = building.GetComponent<BuildingID>();
        if (idComponent != null && !string.IsNullOrEmpty(idComponent.id))
        {
            return idComponent.id;
        }

        // Create new ID
        string newId = System.Guid.NewGuid().ToString();

        // Add BuildingID component if it doesn't exist
        if (idComponent == null)
        {
            idComponent = building.gameObject.AddComponent<BuildingID>();
        }
        idComponent.id = newId;

        return newId;
    }

    /// <summary>
    /// Updates building data from the current state of a building
    /// </summary>
    private void UpdateBuildingDataFromBuilding(BuildingData data, Building building)
    {
        data.buildingName = building.name;
        data.currentLevel = building.level;
        data.maxLevel = building.bankLevels?.Length ?? 1;
        data.position = building.transform.position;
        data.rotation = building.transform.eulerAngles;
        data.scale = building.transform.localScale;
        data.isActive = building.gameObject.activeSelf;
    }

    #region Building Management Methods

    /// <summary>
    /// Gets all building data
    /// </summary>
    public List<BuildingData> GetAllBuildingData()
    {
        return new List<BuildingData>(buildingDataList);
    }

    /// <summary>
    /// Gets building data by ID
    /// </summary>
    public BuildingData GetBuildingData(string id)
    {
        return buildingDataList.FirstOrDefault(bd => bd.id == id);
    }

    /// <summary>
    /// Gets building component by ID
    /// </summary>
    public Building GetBuilding(string id)
    {
        return buildingLookup.ContainsKey(id) ? buildingLookup[id] : null;
    }

    /// <summary>
    /// Gets building data for a specific building component
    /// </summary>
    public BuildingData GetBuildingData(Building building)
    {
        if (reverseLookup.ContainsKey(building))
        {
            string id = reverseLookup[building];
            return GetBuildingData(id);
        }
        return null;
    }

    /// <summary>
    /// Updates building data
    /// </summary>
    public void UpdateBuildingData(string id, BuildingData newData)
    {
        BuildingData existingData = GetBuildingData(id);
        if (existingData != null)
        {
            // Preserve the ID
            newData.id = id;

            // Replace in list
            int index = buildingDataList.IndexOf(existingData);
            buildingDataList[index] = newData;

            // Update the building component if it exists
            Building building = GetBuilding(id);
            if (building != null)
            {
                ApplyDataToBuilding(newData, building);
            }

            OnBuildingUpdated?.Invoke(newData);
        }
    }

    /// <summary>
    /// Applies building data to a building component
    /// </summary>
    private void ApplyDataToBuilding(BuildingData data, Building building)
    {
        building.name = data.buildingName;
        building.transform.position = data.position;
        building.transform.eulerAngles = data.rotation;
        building.transform.localScale = data.scale;
        building.gameObject.SetActive(data.isActive);

        // Update level if different
        if (building.level != data.currentLevel)
        {
            building.level = data.currentLevel;
            building.RefreshDisplay(); // Refresh the building display
        }
    }

    /// <summary>
    /// Removes a building by ID
    /// </summary>
    public void RemoveBuilding(string id)
    {
        BuildingData data = GetBuildingData(id);
        Building building = GetBuilding(id);

        if (data != null)
        {
            buildingDataList.Remove(data);
            OnBuildingRemoved?.Invoke(data);
        }

        if (building != null)
        {
            buildingLookup.Remove(id);
            reverseLookup.Remove(building);

            // Destroy the game object
            if (Application.isPlaying)
            {
                Destroy(building.gameObject);
            }
            else
            {
                DestroyImmediate(building.gameObject);
            }
        }
    }

    /// <summary>
    /// Sets the level of a building
    /// </summary>
    public void SetBuildingLevel(string id, int level)
    {
        BuildingData data = GetBuildingData(id);
        Building building = GetBuilding(id);

        if (data != null && building != null)
        {
            level = Mathf.Clamp(level, 0, data.maxLevel - 1);
            data.currentLevel = level;
            building.level = level;
            building.RefreshDisplay(); // Refresh the building display

            OnBuildingUpdated?.Invoke(data);
        }
    }

    /// <summary>
    /// Increases building level by 1
    /// </summary>
    public void LevelUpBuilding(string id)
    {
        BuildingData data = GetBuildingData(id);
        if (data != null && data.currentLevel < data.maxLevel - 1)
        {
            SetBuildingLevel(id, data.currentLevel + 1);
        }
    }

    /// <summary>
    /// Decreases building level by 1
    /// </summary>
    public void LevelDownBuilding(string id)
    {
        BuildingData data = GetBuildingData(id);
        if (data != null && data.currentLevel > 0)
        {
            SetBuildingLevel(id, data.currentLevel - 1);
        }
    }

    /// <summary>
    /// Renames a building
    /// </summary>
    public void RenameBuilding(string id, string newName)
    {
        BuildingData data = GetBuildingData(id);
        Building building = GetBuilding(id);

        if (data != null)
        {
            data.buildingName = newName;

            if (building != null)
            {
                building.name = newName;
            }

            OnBuildingUpdated?.Invoke(data);
        }
    }

    /// <summary>
    /// Sets building type
    /// </summary>
    public void SetBuildingType(string id, string buildingType)
    {
        BuildingData data = GetBuildingData(id);
        if (data != null)
        {
            data.buildingType = buildingType;
            OnBuildingUpdated?.Invoke(data);
        }
    }

    /// <summary>
    /// Sets building description
    /// </summary>
    public void SetBuildingDescription(string id, string description)
    {
        BuildingData data = GetBuildingData(id);
        if (data != null)
        {
            data.description = description;
            OnBuildingUpdated?.Invoke(data);
        }
    }

    #endregion

    #region Save/Load Functionality

    [System.Serializable]
    public class BuildingManagerSaveData
    {
        public List<BuildingData> buildings;

        public BuildingManagerSaveData()
        {
            buildings = new List<BuildingData>();
        }
    }

    /// <summary>
    /// Saves building data to PlayerPrefs
    /// </summary>
    public void SaveBuildingData()
    {
        BuildingManagerSaveData saveData = new BuildingManagerSaveData();
        saveData.buildings = new List<BuildingData>(buildingDataList);

        string json = JsonUtility.ToJson(saveData, true);
        PlayerPrefs.SetString("BuildingManagerData", json);
        PlayerPrefs.Save();

        Debug.Log("Building data saved successfully.");
    }

    /// <summary>
    /// Loads building data from PlayerPrefs
    /// </summary>
    public void LoadBuildingData()
    {
        if (PlayerPrefs.HasKey("BuildingManagerData"))
        {
            string json = PlayerPrefs.GetString("BuildingManagerData");
            BuildingManagerSaveData saveData = JsonUtility.FromJson<BuildingManagerSaveData>(json);

            if (saveData != null && saveData.buildings != null)
            {
                buildingDataList = saveData.buildings;

                // Update lookups for existing buildings
                RefreshBuildings();

                Debug.Log("Building data loaded successfully.");
            }
        }
        else
        {
            Debug.Log("No saved building data found.");
        }
    }

    /// <summary>
    /// Saves building data to a file
    /// </summary>
    public void SaveBuildingDataToFile(string filePath)
    {
        try
        {
            BuildingManagerSaveData saveData = new BuildingManagerSaveData();
            saveData.buildings = new List<BuildingData>(buildingDataList);

            string json = JsonUtility.ToJson(saveData, true);
            System.IO.File.WriteAllText(filePath, json);

            Debug.Log($"Building data saved to file: {filePath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save building data to file: {e.Message}");
        }
    }

    /// <summary>
    /// Loads building data from a file
    /// </summary>
    public void LoadBuildingDataFromFile(string filePath)
    {
        try
        {
            if (System.IO.File.Exists(filePath))
            {
                string json = System.IO.File.ReadAllText(filePath);
                BuildingManagerSaveData saveData = JsonUtility.FromJson<BuildingManagerSaveData>(json);

                if (saveData != null && saveData.buildings != null)
                {
                    buildingDataList = saveData.buildings;

                    // Update lookups for existing buildings
                    RefreshBuildings();

                    Debug.Log($"Building data loaded from file: {filePath}");
                }
            }
            else
            {
                Debug.LogWarning($"File not found: {filePath}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load building data from file: {e.Message}");
        }
    }

    /// <summary>
    /// Clears all building data
    /// </summary>
    public void ClearBuildingData()
    {
        buildingDataList.Clear();
        buildingLookup.Clear();
        reverseLookup.Clear();

        OnBuildingsRefreshed?.Invoke();

        Debug.Log("Building data cleared.");
    }

    #endregion

    #region Unity Editor Methods

#if UNITY_EDITOR
    /// <summary>
    /// Context menu method to refresh buildings in editor
    /// </summary>
    [UnityEngine.ContextMenu("Refresh Buildings")]
    public void RefreshBuildingsEditor()
    {
        RefreshBuildings();
        UnityEditor.EditorUtility.SetDirty(this);
    }

    /// <summary>
    /// Context menu method to save building data in editor
    /// </summary>
    [UnityEngine.ContextMenu("Save Building Data")]
    public void SaveBuildingDataEditor()
    {
        SaveBuildingData();
    }

    /// <summary>
    /// Context menu method to load building data in editor
    /// </summary>
    [UnityEngine.ContextMenu("Load Building Data")]
    public void LoadBuildingDataEditor()
    {
        LoadBuildingData();
    }
#endif

    #endregion
}

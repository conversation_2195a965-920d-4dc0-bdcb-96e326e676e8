Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker30
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker30.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [66348]  Target information:

Player connection [66348]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1153795739 [EditorId] 1153795739 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [66348] Host joined multi-casting on [***********:54997]...
Player connection [66348] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56724
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002641 seconds.
- Loaded All Assemblies, in  0.407 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 216 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.528 seconds
Domain Reload Profiling: 935ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (174ms)
		LoadAssemblies (132ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (170ms)
			TypeCache.Refresh (168ms)
				TypeCache.ScanAssembly (151ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (529ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (483ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (289ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (45ms)
			ProcessInitializeOnLoadAttributes (96ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.732 seconds
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.783 seconds
Domain Reload Profiling: 1512ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (529ms)
		LoadAssemblies (353ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (253ms)
			TypeCache.Refresh (184ms)
				TypeCache.ScanAssembly (166ms)
			BuildScriptInfoCaches (50ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (784ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (648ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (202ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 205 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7348 unused Assets / (7.4 MB). Loaded Objects now: 7996.
Memory consumption went from 178.7 MB to 171.3 MB.
Total: 15.569900 ms (FindLiveObjects: 1.705300 ms CreateObjectMapping: 1.310400 ms MarkObjects: 7.465100 ms  DeleteObjects: 5.084500 ms)

========================================================================
Received Import Request.
  Time since last request: 1562856.509317 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01C.prefab
  artifactKey: Guid(3b557128f00ffc845baf837304e413a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01C.prefab using Guid(3b557128f00ffc845baf837304e413a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a2294c9b2183514cf93df259d2102781') in 0.4926422 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 179

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03C.prefab
  artifactKey: Guid(36abb70a70289b24fa085792a077d05e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03C.prefab using Guid(36abb70a70289b24fa085792a077d05e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '17ba9708b576f5d0555c6040d198971b') in 0.0440879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03A.prefab
  artifactKey: Guid(e4e3ca7f146732e4d8d8beb556f39a65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03A.prefab using Guid(e4e3ca7f146732e4d8d8beb556f39a65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b761243c354d1109602abe8e4ac8fef0') in 0.0288864 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01E.prefab
  artifactKey: Guid(d7f8c29fa894d454cbb0f981661ad11a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_01E.prefab using Guid(d7f8c29fa894d454cbb0f981661ad11a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2721c12c04df28151126a47af99dce3b') in 0.0287825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 178

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_02B.prefab
  artifactKey: Guid(0b62877cb4ffd2c4abaec1d11bb9c8b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_02B.prefab using Guid(0b62877cb4ffd2c4abaec1d11bb9c8b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44dc1f05df95d48b768905e44fc3375e') in 0.0285967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 162

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.755 seconds
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.792 seconds
Domain Reload Profiling: 1548ms
	BeginReloadAssembly (203ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (490ms)
		LoadAssemblies (381ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (793ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (581ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (283ms)
			ProcessInitializeOnLoadMethodAttributes (192ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 5.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 57 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7360 unused Assets / (9.7 MB). Loaded Objects now: 8045.
Memory consumption went from 194.4 MB to 184.7 MB.
Total: 24.451700 ms (FindLiveObjects: 1.422600 ms CreateObjectMapping: 3.118300 ms MarkObjects: 9.803900 ms  DeleteObjects: 10.104000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1284.723279 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03D.prefab
  artifactKey: Guid(8cb02f48edeba1a4298ed7b89bcc844e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_03D.prefab using Guid(8cb02f48edeba1a4298ed7b89bcc844e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4bd37bf58d034687e1afb48e9105aef0') in 1.2124571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 179

========================================================================
Received Import Request.
  Time since last request: 0.000162 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02A.prefab
  artifactKey: Guid(7bf341f9cf9d36b448ab76f26cb76753) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02A.prefab using Guid(7bf341f9cf9d36b448ab76f26cb76753) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2bcb33244fc8c0221ba101f5c34991e3') in 0.0426166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01B.prefab
  artifactKey: Guid(d5eed18bad6c6ca4b8f9b64eeb5ff205) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01B.prefab using Guid(d5eed18bad6c6ca4b8f9b64eeb5ff205) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a48fb2cbe01971d803524cb0137f2e84') in 0.0622378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02C.prefab
  artifactKey: Guid(4ef6b1e8ec173e94191d9aa742d1f863) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02C.prefab using Guid(4ef6b1e8ec173e94191d9aa742d1f863) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc846cf048bee3ce0fa3af189b5b8f1a') in 0.0446912 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03D.prefab
  artifactKey: Guid(6956006f4f003e34089ca3953024b535) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03D.prefab using Guid(6956006f4f003e34089ca3953024b535) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b92024a6f94ee6027ed2d64f5bf022d') in 0.0245788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01E.prefab
  artifactKey: Guid(54817b8116307184aaa20d5a7e4b7376) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01E.prefab using Guid(54817b8116307184aaa20d5a7e4b7376) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f47fd34a3f398f013ba1362d9ade3e3c') in 0.0187769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03E.prefab
  artifactKey: Guid(5c8b757d02f3a6940a27a2be420d1c73) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_03E.prefab using Guid(5c8b757d02f3a6940a27a2be420d1c73) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '607f28e92abc2a6aa5c959739f378920') in 0.0247733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 188

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02D.prefab
  artifactKey: Guid(d263fb1b77f412449a48e3c62955e0ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_02D.prefab using Guid(d263fb1b77f412449a48e3c62955e0ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7838b944dda6a5f32a0b58703ef616ba') in 0.0234032 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 164

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01C.prefab
  artifactKey: Guid(212177bae3b324b49aa4907cc84984c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01C.prefab using Guid(212177bae3b324b49aa4907cc84984c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4c4466040665f1a8cb73697872a56c43') in 0.0482286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01D.prefab
  artifactKey: Guid(cf553440dc5aa254fb0720ac042139d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Male_Character_01D.prefab using Guid(cf553440dc5aa254fb0720ac042139d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'adaf5352d20c28dff17f45bf0e6f8dee') in 0.0339532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 174

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_02B.prefab
  artifactKey: Guid(0b62877cb4ffd2c4abaec1d11bb9c8b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Characters Variants/TSP_Female_Character_02B.prefab using Guid(0b62877cb4ffd2c4abaec1d11bb9c8b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3dbab3f48542434f735820ed363ba1c7') in 0.0246455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 162

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0